package main

import (
	"encoding/json"
	"fmt"
	"time"

	"pay-core/internal/model"
	"pay-core/pkg/types"
	"github.com/shopspring/decimal"
)

func main() {
	fmt.Println("测试时间格式化...")

	// 创建一个测试订单
	order := &model.PaymentOrder{
		ID:          1,
		OrderNo:     "TEST_ORDER_001",
		AppID:       1,
		AppOrderNo:  "APP_ORDER_001",
		PayChannel:  "ALIPAY",
		TotalAmount: decimal.NewFromFloat(100.50),
		Subject:     "测试订单",
		Status:      model.OrderStatusWaitingPay,
		ExpireTime:  types.Now().Add(30 * time.Minute),
		CreatedAt:   types.Now(),
		UpdatedAt:   types.Now(),
	}

	// 序列化为JSON
	jsonData, err := json.MarshalIndent(order, "", "  ")
	if err != nil {
		fmt.Printf("JSON序列化失败: %v\n", err)
		return
	}

	fmt.Println("订单JSON格式:")
	fmt.Println(string(jsonData))

	// 测试自定义时间类型
	fmt.Println("\n测试自定义时间类型:")
	now := types.Now()
	fmt.Printf("当前时间: %s\n", now.String())
	fmt.Printf("时间戳: %d\n", now.Unix())

	// 测试时间解析
	timeStr := "2024-01-01 12:00:00"
	var parsedTime types.Time
	err = json.Unmarshal([]byte(`"`+timeStr+`"`), &parsedTime)
	if err != nil {
		fmt.Printf("时间解析失败: %v\n", err)
		return
	}
	fmt.Printf("解析的时间: %s\n", parsedTime.String())

	// 测试零值时间
	var zeroTime types.Time
	zeroJSON, _ := json.Marshal(zeroTime)
	fmt.Printf("零值时间JSON: %s\n", string(zeroJSON))

	fmt.Println("\n时间格式化测试完成！")
}