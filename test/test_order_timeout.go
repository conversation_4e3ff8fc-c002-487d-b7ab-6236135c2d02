package main

import (
	"fmt"
	"time"

	"pay-core/internal/model"
	"pay-core/pkg/types"
	"github.com/shopspring/decimal"
)

func main() {
	fmt.Println("=== 测试订单超时时间设置 ===\n")

	// 模拟创建订单请求（不设置ExpireMinutes，使用默认值）
	req := &model.CreateOrderRequest{
		Amount:         decimal.NewFromFloat(99.99),
		Subject:        "测试订单",
		PaymentChannel: "ALIPAY",
		// ExpireMinutes 不设置，应该使用默认的30分钟
	}

	fmt.Printf("创建订单请求:\n")
	fmt.Printf("- 金额: %s 元\n", req.Amount.String())
	fmt.Printf("- 标题: %s\n", req.Subject)
	fmt.Printf("- 支付渠道: %s\n", req.PaymentChannel)
	fmt.Printf("- 过期分钟数: %d (0表示使用默认值)\n", req.ExpireMinutes)

	// 模拟服务层逻辑
	if req.ExpireMinutes == 0 {
		req.ExpireMinutes = 30 // 默认30分钟过期
	}
	
	now := types.Now()
	expireTime := now.Add(time.Duration(req.ExpireMinutes) * time.Minute)

	fmt.Printf("\n计算结果:\n")
	fmt.Printf("- 当前时间: %s\n", now.String())
	fmt.Printf("- 过期时间: %s\n", expireTime.String())
	fmt.Printf("- 超时时长: %d 分钟\n", req.ExpireMinutes)
	fmt.Printf("- 时间差: %v\n", expireTime.Sub(now))

	// 验证时间差是否正确
	expectedDuration := 30 * time.Minute
	actualDuration := expireTime.Sub(now)
	
	if actualDuration == expectedDuration {
		fmt.Printf("\n✅ 测试通过：订单超时时间正确设置为30分钟\n")
	} else {
		fmt.Printf("\n❌ 测试失败：期望%v，实际%v\n", expectedDuration, actualDuration)
	}
}