package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"pay-core/internal/config"
	"pay-core/internal/model"
	"pay-core/pkg/database"
	"pay-core/pkg/payment"
	"pay-core/pkg/types"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

func testRefundAPI() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db, err := database.NewMySQL(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 创建支付客户端
	paymentClient, err := payment.NewClient(cfg.Payment)
	if err != nil {
		log.Fatalf("Failed to create payment client: %v", err)
	}

	fmt.Println("=== 微信支付退款API测试 ===")

	// 1. 首先创建一个已支付的订单用于测试退款
	testOrder := createTestPaidOrder(db)
	fmt.Printf("创建测试订单: %s\n", testOrder.OrderNo)

	// 2. 测试退款申请
	testRefundRequest(paymentClient, testOrder)

	// 3. 模拟退款回调
	testRefundCallback()

	fmt.Println("=== 退款API测试完成 ===")
}

// createTestPaidOrder 创建一个已支付的测试订单
func createTestPaidOrder(db *gorm.DB) *model.PaymentOrder {
	orderNo := fmt.Sprintf("TEST_REFUND_%d", time.Now().Unix())

	order := &model.PaymentOrder{
		OrderNo:     orderNo,
		AppID:       1, // 假设存在ID为1的应用
		AppOrderNo:  fmt.Sprintf("APP_%d", time.Now().Unix()),
		TotalAmount: decimal.NewFromFloat(100.00),
		Subject:     "退款测试订单",
		Body:        "这是一个用于测试退款的订单",
		PayChannel:  "wechat",
		Status:      model.OrderStatusPaid, // 设置为已支付状态
		OutTradeNo:  fmt.Sprintf("wx_%d", time.Now().Unix()),
		PaidAt:      &types.Time{Time: time.Now()},
		ExpireTime:  types.Time{Time: time.Now().Add(30 * time.Minute)},
		PayerInfo: model.JSON{
			"transaction_id": fmt.Sprintf("wx_trans_%d", time.Now().Unix()),
		},
	}

	if err := db.Create(order).Error; err != nil {
		log.Fatalf("Failed to create test order: %v", err)
	}

	return order
}

// testRefundRequest 测试退款申请
func testRefundRequest(client *payment.Client, order *model.PaymentOrder) {
	fmt.Println("\n--- 测试退款申请 ---")

	refundReq := &payment.RefundRequest{
		OrderNo:       order.OrderNo,
		RefundNo:      fmt.Sprintf("REFUND_%d", time.Now().Unix()),
		TransactionID: order.OutTradeNo,
		RefundAmount:  decimal.NewFromFloat(50.00), // 部分退款
		TotalAmount:   order.TotalAmount,
		RefundReason:  "用户申请退款",
		NotifyURL:     "http://localhost:8080/notify/wechat/refund",
		Channel:       "wechat",
	}

	fmt.Printf("退款请求参数:\n")
	fmt.Printf("  订单号: %s\n", refundReq.OrderNo)
	fmt.Printf("  退款单号: %s\n", refundReq.RefundNo)
	fmt.Printf("  退款金额: %s\n", refundReq.RefundAmount.String())
	fmt.Printf("  退款原因: %s\n", refundReq.RefundReason)

	// 注意：这里会调用真实的微信支付API，需要确保配置正确
	// 在测试环境中，可能需要模拟这个调用
	resp, err := client.Refund(refundReq)
	if err != nil {
		fmt.Printf("退款申请失败: %v\n", err)
		// 在测试环境中，这是预期的，因为我们没有真实的微信支付配置
		fmt.Println("注意：这是预期的错误，因为测试环境没有真实的微信支付配置")
		return
	}

	fmt.Printf("退款申请成功:\n")
	fmt.Printf("  退款单号: %s\n", resp.RefundNo)
	fmt.Printf("  第三方退款单号: %s\n", resp.OutRefundNo)
	fmt.Printf("  退款状态: %s\n", resp.Status)
	fmt.Printf("  退款时间: %s\n", resp.RefundTime)
}

// testRefundCallback 测试退款回调
func testRefundCallback() {
	fmt.Println("\n--- 测试退款回调 ---")

	// 模拟微信支付退款回调数据
	callbackData := map[string]interface{}{
		"event_type": "REFUND.SUCCESS",
		"resource": map[string]interface{}{
			"ciphertext":      "模拟加密数据",
			"associated_data": "模拟关联数据",
			"nonce":           "模拟随机数",
		},
	}

	jsonData, _ := json.Marshal(callbackData)

	// 发送回调请求到本地服务器
	resp, err := http.Post(
		"http://localhost:8080/notify/wechat/refund",
		"application/json",
		bytes.NewBuffer(jsonData),
	)

	if err != nil {
		fmt.Printf("发送退款回调失败: %v\n", err)
		fmt.Println("注意：请确保服务器正在运行在 localhost:8080")
		return
	}
	defer resp.Body.Close()

	fmt.Printf("退款回调响应状态: %d\n", resp.StatusCode)

	if resp.StatusCode == 200 {
		fmt.Println("退款回调处理成功")
	} else {
		fmt.Println("退款回调处理失败")
	}
}
