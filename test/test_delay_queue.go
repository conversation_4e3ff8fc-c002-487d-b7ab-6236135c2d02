package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"pay-core/internal/config"
	"pay-core/pkg/queue"

	"github.com/redis/go-redis/v9"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接Redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     cfg.Redis.Addr,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})

	// 测试Redis连接
	ctx := context.Background()
	if err := rdb.Ping(ctx).Err(); err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}

	fmt.Println("=== Redis延迟队列功能测试 ===")

	// 创建延迟队列
	delayQueue := queue.NewDelayQueue(rdb, "test_pay_core")

	// 1. 测试订单过期任务
	testOrderExpireTasks(delayQueue)

	// 2. 测试订单轮询任务
	testOrderPollTasks(delayQueue)

	// 3. 测试队列统计
	testQueueStats(delayQueue)

	// 4. 测试任务移除
	testTaskRemoval(delayQueue)

	fmt.Println("=== 延迟队列功能测试完成 ===")
}

// testOrderExpireTasks 测试订单过期任务
func testOrderExpireTasks(dq *queue.DelayQueue) {
	fmt.Println("\n--- 测试订单过期任务 ---")

	ctx := context.Background()
	orderNo := fmt.Sprintf("TEST_EXPIRE_%d", time.Now().Unix())

	// 创建一个5秒后过期的任务
	expireTime := time.Now().Add(5 * time.Second)
	task := queue.CreateOrderExpireTask(orderNo, expireTime)

	// 添加任务
	if err := dq.AddTask(ctx, task); err != nil {
		fmt.Printf("添加过期任务失败: %v\n", err)
		return
	}

	fmt.Printf("添加过期任务成功:\n")
	fmt.Printf("  订单号: %s\n", orderNo)
	fmt.Printf("  过期时间: %s\n", expireTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("  任务ID: %s\n", task.ID)

	// 等待3秒，任务应该还没准备好
	fmt.Println("\n等待3秒，检查任务状态...")
	time.Sleep(3 * time.Second)

	readyTasks, err := dq.GetReadyTasks(ctx, queue.TaskTypeOrderExpire, 10)
	if err != nil {
		fmt.Printf("获取准备任务失败: %v\n", err)
		return
	}

	fmt.Printf("准备执行的过期任务数: %d\n", len(readyTasks))

	// 再等待3秒，任务应该准备好了
	fmt.Println("\n再等待3秒，任务应该准备好...")
	time.Sleep(3 * time.Second)

	readyTasks, err = dq.GetReadyTasks(ctx, queue.TaskTypeOrderExpire, 10)
	if err != nil {
		fmt.Printf("获取准备任务失败: %v\n", err)
		return
	}

	fmt.Printf("准备执行的过期任务数: %d\n", len(readyTasks))
	for _, task := range readyTasks {
		fmt.Printf("  任务ID: %s, 订单号: %s\n", task.ID, task.Data["order_no"])
	}
}

// testOrderPollTasks 测试订单轮询任务
func testOrderPollTasks(dq *queue.DelayQueue) {
	fmt.Println("\n--- 测试订单轮询任务 ---")

	ctx := context.Background()
	orderNo := fmt.Sprintf("TEST_POLL_%d", time.Now().Unix())

	// 创建多个轮询任务（模拟递增间隔）
	pollTimes := []time.Duration{
		2 * time.Second, // 第1次轮询
		4 * time.Second, // 第2次轮询
		6 * time.Second, // 第3次轮询
	}

	for i, delay := range pollTimes {
		pollCount := i + 1
		pollTime := time.Now().Add(delay)
		task := queue.CreateOrderPollTask(orderNo, pollTime, pollCount)

		if err := dq.AddTask(ctx, task); err != nil {
			fmt.Printf("添加轮询任务失败: %v\n", err)
			continue
		}

		fmt.Printf("添加轮询任务 %d:\n", pollCount)
		fmt.Printf("  订单号: %s\n", orderNo)
		fmt.Printf("  轮询时间: %s\n", pollTime.Format("2006-01-02 15:04:05"))
		fmt.Printf("  任务ID: %s\n", task.ID)
	}

	// 等待并检查轮询任务
	fmt.Println("\n等待轮询任务准备...")
	for i := 0; i < 8; i++ {
		time.Sleep(1 * time.Second)

		readyTasks, err := dq.GetReadyTasks(ctx, queue.TaskTypeOrderPoll, 10)
		if err != nil {
			fmt.Printf("获取轮询任务失败: %v\n", err)
			continue
		}

		if len(readyTasks) > 0 {
			fmt.Printf("第 %d 秒: 准备执行的轮询任务数: %d\n", i+1, len(readyTasks))
			for _, task := range readyTasks {
				pollCount := int(task.Data["poll_count"].(float64))
				fmt.Printf("  执行轮询任务: 订单号=%s, 轮询次数=%d\n",
					task.Data["order_no"], pollCount)
			}
		}
	}
}

// testQueueStats 测试队列统计
func testQueueStats(dq *queue.DelayQueue) {
	fmt.Println("\n--- 测试队列统计 ---")

	ctx := context.Background()

	// 添加一些测试任务
	orderNo := fmt.Sprintf("TEST_STATS_%d", time.Now().Unix())

	// 添加即将到期的任务
	task1 := queue.CreateOrderExpireTask(orderNo+"_1", time.Now().Add(1*time.Second))
	dq.AddTask(ctx, task1)

	// 添加未来的任务
	task2 := queue.CreateOrderExpireTask(orderNo+"_2", time.Now().Add(1*time.Hour))
	dq.AddTask(ctx, task2)

	// 获取统计信息
	stats, err := dq.GetQueueStats(ctx, queue.TaskTypeOrderExpire)
	if err != nil {
		fmt.Printf("获取队列统计失败: %v\n", err)
		return
	}

	fmt.Printf("订单过期队列统计:\n")
	for key, value := range stats {
		fmt.Printf("  %s: %v\n", key, value)
	}

	// 等待1秒后再次检查
	time.Sleep(2 * time.Second)

	stats, err = dq.GetQueueStats(ctx, queue.TaskTypeOrderExpire)
	if err != nil {
		fmt.Printf("获取队列统计失败: %v\n", err)
		return
	}

	fmt.Printf("\n2秒后的队列统计:\n")
	for key, value := range stats {
		fmt.Printf("  %s: %v\n", key, value)
	}
}

// testTaskRemoval 测试任务移除
func testTaskRemoval(dq *queue.DelayQueue) {
	fmt.Println("\n--- 测试任务移除 ---")

	ctx := context.Background()
	orderNo := fmt.Sprintf("TEST_REMOVE_%d", time.Now().Unix())

	// 添加任务
	expireTime := time.Now().Add(1 * time.Hour) // 1小时后过期
	task := queue.CreateOrderExpireTask(orderNo, expireTime)

	if err := dq.AddTask(ctx, task); err != nil {
		fmt.Printf("添加任务失败: %v\n", err)
		return
	}

	fmt.Printf("添加任务成功: %s\n", task.ID)

	// 检查任务是否存在
	stats, _ := dq.GetQueueStats(ctx, queue.TaskTypeOrderExpire)
	fmt.Printf("添加后队列任务数: %v\n", stats["total_tasks"])

	// 移除任务
	if err := dq.RemoveTask(ctx, queue.TaskTypeOrderExpire, task.ID); err != nil {
		fmt.Printf("移除任务失败: %v\n", err)
		return
	}

	fmt.Printf("移除任务成功: %s\n", task.ID)

	// 再次检查任务数
	stats, _ = dq.GetQueueStats(ctx, queue.TaskTypeOrderExpire)
	fmt.Printf("移除后队列任务数: %v\n", stats["total_tasks"])
}

// 清理测试数据
func cleanupTestData(dq *queue.DelayQueue) {
	fmt.Println("\n--- 清理测试数据 ---")

	ctx := context.Background()

	// 清空所有测试队列
	if err := dq.ClearQueue(ctx, queue.TaskTypeOrderExpire); err != nil {
		fmt.Printf("清空过期队列失败: %v\n", err)
	} else {
		fmt.Println("清空过期队列成功")
	}

	if err := dq.ClearQueue(ctx, queue.TaskTypeOrderPoll); err != nil {
		fmt.Printf("清空轮询队列失败: %v\n", err)
	} else {
		fmt.Println("清空轮询队列成功")
	}
}
