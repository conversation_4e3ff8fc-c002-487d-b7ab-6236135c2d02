package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"pay-core/internal/config"

	"github.com/go-pay/gopay"
	wechatv3 "github.com/go-pay/gopay/wechat/v3"
	"github.com/shopspring/decimal"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 读取私钥
	keyBytes, err := os.ReadFile(cfg.Payment.Wechat.PrivateKeyPath)
	if err != nil {
		log.Fatalf("Failed to read private key: %v", err)
	}
	privateKeyContent := string(keyBytes)

	// 创建微信支付客户端
	client, err := wechatv3.NewClientV3(
		cfg.Payment.Wechat.MchID,
		cfg.Payment.Wechat.SerialNo,
		cfg.Payment.Wechat.APIv3Key,
		privateKeyContent,
	)
	if err != nil {
		log.Fatalf("Failed to create wechat client: %v", err)
	}

	fmt.Println("微信支付客户端创建成功，开始测试API调用...")

	// 构建测试订单参数
	orderNo := fmt.Sprintf("TEST_%d", time.Now().Unix())
	amount := decimal.NewFromFloat(1.00) // 1分钱测试

	bm := make(gopay.BodyMap)
	bm.Set("appid", cfg.Payment.Wechat.AppID).
		Set("mchid", cfg.Payment.Wechat.MchID).
		Set("description", "测试订单").
		Set("out_trade_no", orderNo).
		Set("notify_url", "http://localhost:8080/notify/wechat").
		SetBodyMap("amount", func(bm gopay.BodyMap) {
			bm.Set("total", amount.Mul(decimal.NewFromInt(100)).IntPart()). // 转换为分
				Set("currency", "CNY")
		})

	fmt.Printf("请求参数: %+v\n", bm)

	// 调用微信支付V3 Native下单API
	ctx := context.Background()
	fmt.Println("开始调用微信支付V3 Native API...")
	
	wxRsp, err := client.V3TransactionNative(ctx, bm)
	if err != nil {
		fmt.Printf("API调用失败: %v\n", err)
		return
	}

	fmt.Printf("API响应状态码: %d\n", wxRsp.Code)
	fmt.Printf("API响应错误信息: %s\n", wxRsp.Error)
	
	if wxRsp.Code == 200 {
		fmt.Println("API调用成功!")
		if wxRsp.Response != nil {
			fmt.Printf("二维码URL: %s\n", wxRsp.Response.CodeUrl)
		}
	} else {
		fmt.Printf("API调用失败，状态码: %d，错误: %s\n", wxRsp.Code, wxRsp.Error)
	}
}