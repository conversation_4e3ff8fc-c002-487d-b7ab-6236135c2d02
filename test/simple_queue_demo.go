package main

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
)

func main() {
	fmt.Println("=== 简单延迟队列测试 ===")

	// 测试创建订单
	orderNo := createTestOrder()
	if orderNo == "" {
		fmt.Println("创建订单失败")
		return
	}

	fmt.Printf("订单创建成功: %s\n", orderNo)
	fmt.Println("延迟队列任务已添加，请查看服务器日志")
	fmt.Println("预期日志内容:")
	fmt.Println("1. 'Added order expire task to queue'")
	fmt.Println("2. 'Added order poll task to queue'")
	fmt.Println("3. 30秒后: 'Processing order poll tasks'")
}

func createTestOrder() string {
	url := "http://localhost:8080/api/v1/orders"

	// 构建请求体
	requestBody := map[string]interface{}{
		"amount":            100.00,
		"subject":           "延迟队列测试订单",
		"body":              "测试Redis延迟队列功能",
		"app_user_id":       "100000064787",
		"merchant_order_no": fmt.Sprintf("QUEUE_TEST_%d", time.Now().Unix()),
		"payment_method":    "native",
		"payment_channel":   "wechat",
		"expire_minutes":    30,
	}

	jsonData, _ := json.Marshal(requestBody)

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("创建请求失败: %v\n", err)
		return ""
	}

	// 添加认证头
	addAuthHeaders(req, string(jsonData))

	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("发送请求失败: %v\n", err)
		return ""
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return ""
	}

	fmt.Printf("创建订单响应 (状态码: %d):\n%s\n", resp.StatusCode, string(respBody))

	// 解析响应获取订单号
	if resp.StatusCode == 200 {
		var response map[string]interface{}
		if err := json.Unmarshal(respBody, &response); err == nil {
			if data, ok := response["data"].(map[string]interface{}); ok {
				if orderNo, ok := data["order_no"].(string); ok {
					return orderNo
				}
			}
		}
	}

	return ""
}

func addAuthHeaders(req *http.Request, body string) {
	clientID := "robot_001"
	appSecret := "7Hk9pJ2fTdR8sLq4wVbN5zKmG6xYcX3F"
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := fmt.Sprintf("test_nonce_%d", time.Now().UnixNano())

	// 构建参数字典
	params := make(map[string]string)

	// 解析JSON参数
	if body != "" {
		var jsonParams map[string]interface{}
		if err := json.Unmarshal([]byte(body), &jsonParams); err == nil {
			for key, value := range jsonParams {
				params[key] = fmt.Sprintf("%v", value)
			}
		}
	}

	// 添加认证参数
	params["api_key"] = clientID
	params["timestamp"] = timestamp
	params["nonce"] = nonce

	// 计算签名
	signature := calcSignature(params, appSecret)

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-API-Key", clientID)
	req.Header.Set("X-Timestamp", timestamp)
	req.Header.Set("X-Nonce", nonce)
	req.Header.Set("X-Signature", signature)

	fmt.Printf("认证信息:\n")
	fmt.Printf("  API-Key: %s\n", clientID)
	fmt.Printf("  Timestamp: %s\n", timestamp)
	fmt.Printf("  Nonce: %s\n", nonce)
	fmt.Printf("  Signature: %s\n\n", signature)
}

func calcSignature(params map[string]string, appSecret string) string {
	// 1. 获取所有键并排序
	keys := make([]string, 0, len(params))
	for key := range params {
		if key != "signature" && params[key] != "" {
			keys = append(keys, key)
		}
	}
	sort.Strings(keys)

	// 2. 构建签名字符串
	var parts []string
	for _, key := range keys {
		parts = append(parts, fmt.Sprintf("%s=%s", key, params[key]))
	}

	signStr := strings.Join(parts, "&") + "&key=" + appSecret
	fmt.Printf("签名字符串: %s\n", signStr)

	// 3. 计算HMAC-SHA256
	h := hmac.New(sha256.New, []byte(appSecret))
	h.Write([]byte(signStr))

	return hex.EncodeToString(h.Sum(nil))
}
