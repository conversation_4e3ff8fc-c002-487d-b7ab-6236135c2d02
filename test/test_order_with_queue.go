package main

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
)

// 测试创建订单并验证延迟队列功能
func main() {
	fmt.Println("=== 测试订单创建和延迟队列功能 ===")

	// 1. 测试创建订单
	orderNo := testCreateOrder()
	if orderNo == "" {
		fmt.Println("创建订单失败，退出测试")
		return
	}

	fmt.Printf("订单创建成功: %s\n", orderNo)
	fmt.Println("延迟队列任务已添加:")
	fmt.Println("  - 订单过期任务: 30分钟后执行")
	fmt.Println("  - 首次轮询任务: 30秒后执行")

	// 2. 等待一段时间，观察轮询效果
	fmt.Println("\n等待35秒，观察轮询任务执行...")
	time.Sleep(35 * time.Second)

	// 3. 查询订单状态
	testQueryOrder(orderNo)

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("请查看服务器日志，观察以下内容:")
	fmt.Println("1. 订单创建时添加延迟队列任务的日志")
	fmt.Println("2. 30秒后轮询任务执行的日志")
	fmt.Println("3. 轮询查询微信支付状态的日志")
}

// testCreateOrder 测试创建订单
func testCreateOrder() string {
	url := "http://localhost:8080/api/v1/orders"

	// 构建请求体
	requestBody := map[string]interface{}{
		"amount":            100.00,
		"subject":           "延迟队列测试订单",
		"body":              "测试Redis延迟队列功能",
		"app_user_id":       "100000064787",
		"merchant_order_no": fmt.Sprintf("QUEUE_TEST_%d", time.Now().Unix()),
		"payment_method":    "native",
		"payment_channel":   "wechat",
		"expire_minutes":    30,
	}

	jsonData, _ := json.Marshal(requestBody)

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("创建请求失败: %v\n", err)
		return ""
	}

	// 添加认证头
	addAuthHeaders(req, string(jsonData))

	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("发送请求失败: %v\n", err)
		return ""
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return ""
	}

	fmt.Printf("创建订单响应 (状态码: %d):\n%s\n", resp.StatusCode, string(respBody))

	// 解析响应获取订单号
	if resp.StatusCode == 200 {
		var response map[string]interface{}
		if err := json.Unmarshal(respBody, &response); err == nil {
			if data, ok := response["data"].(map[string]interface{}); ok {
				if orderNo, ok := data["order_no"].(string); ok {
					return orderNo
				}
			}
		}
	}

	return ""
}

// testQueryOrder 测试查询订单
func testQueryOrder(orderNo string) {
	url := fmt.Sprintf("http://localhost:8080/api/v1/orders/%s", orderNo)

	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		fmt.Printf("创建查询请求失败: %v\n", err)
		return
	}

	// 添加认证头
	addAuthHeaders(req, "")

	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("发送查询请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取查询响应失败: %v\n", err)
		return
	}

	fmt.Printf("查询订单响应 (状态码: %d):\n%s\n", resp.StatusCode, string(respBody))
}

// addAuthHeaders 添加认证头
func addAuthHeaders(req *http.Request, body string) {
	clientID := "robot_001"
	appSecret := "7Hk9pJ2fTdR8sLq4wVbN5zKmG6xYcX3F"
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := fmt.Sprintf("test_nonce_%d", time.Now().UnixNano())

	// 构建签名字符串（按照现有的认证逻辑）
	method := req.Method
	url := req.URL.String()

	// 构建参数字符串
	params := map[string]string{
		"client_id": clientID,
		"timestamp": timestamp,
		"nonce":     nonce,
	}

	// 如果有body，添加到参数中
	if body != "" {
		params["body"] = body
	}

	// 按字母顺序排序参数
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}

	var paramPairs []string
	for _, k := range keys {
		paramPairs = append(paramPairs, fmt.Sprintf("%s=%s", k, params[k]))
	}

	signString := fmt.Sprintf("%s&%s&%s", method, url, strings.Join(paramPairs, "&"))

	// 计算签名
	h := hmac.New(sha256.New, []byte(appSecret))
	h.Write([]byte(signString))
	signature := hex.EncodeToString(h.Sum(nil))

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-API-Key", clientID)
	req.Header.Set("X-Timestamp", timestamp)
	req.Header.Set("X-Nonce", nonce)
	req.Header.Set("X-Signature", signature)

	fmt.Printf("认证信息:\n")
	fmt.Printf("  API-Key: %s\n", clientID)
	fmt.Printf("  Timestamp: %s\n", timestamp)
	fmt.Printf("  Nonce: %s\n", nonce)
	fmt.Printf("  Signature: %s\n", signature)
	fmt.Printf("  Sign-String: %s\n\n", signString)
}
