package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"pay-core/internal/config"
	"pay-core/internal/model"
	"pay-core/internal/repository"
	"pay-core/internal/service"
	"pay-core/pkg/database"
	"pay-core/pkg/logger"
	"pay-core/pkg/payment"

	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
)

// 测试自动对账功能
func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logger.Init(cfg.Log)

	// 初始化数据库
	db, err := database.NewMySQL(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 初始化Redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     cfg.Redis.Addr,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})

	// 初始化支付客户端
	paymentClient, err := payment.NewClient(cfg.Payment)
	if err != nil {
		log.Fatalf("Failed to initialize payment client: %v", err)
	}

	// 初始化Repository和Service
	merchantRepo := repository.NewMerchantRepository(db)
	orderRepo := repository.NewOrderRepository(db)
	reconcileRepo := repository.NewReconcileRepository(db)
	paymentService := service.NewPaymentService(orderRepo, merchantRepo, paymentClient, rdb)
	reconcileService := service.NewReconcileService(reconcileRepo, orderRepo, paymentClient)
	schedulerService := service.NewSchedulerService(paymentService, reconcileService, rdb, cfg.Scheduler)

	fmt.Println("=== 自动对账功能测试 ===")

	// 1. 创建一些测试订单（模拟昨天的订单）
	fmt.Println("1. 创建测试订单...")
	yesterday := time.Now().AddDate(0, 0, -1)
	
	testOrders := []*model.PaymentOrder{
		{
			OrderNo:         fmt.Sprintf("TEST_ALIPAY_%d", time.Now().Unix()),
			MerchantID:      1,
			MerchantOrderNo: fmt.Sprintf("MERCHANT_ALIPAY_%d", time.Now().Unix()),
			Subject:         "支付宝测试订单",
			Body:            "测试自动对账功能",
			Amount:          decimal.NewFromFloat(100.00),
			Currency:        "CNY",
			PaymentMethod:   "native",
			PaymentChannel:  "alipay",
			Status:          model.OrderStatusPaid,
			PaidAt:          &yesterday,
			ExpiredAt:       yesterday.Add(2 * time.Hour),
			CreatedAt:       yesterday,
			UpdatedAt:       yesterday,
		},
		{
			OrderNo:         fmt.Sprintf("TEST_WECHAT_%d", time.Now().Unix()),
			MerchantID:      1,
			MerchantOrderNo: fmt.Sprintf("MERCHANT_WECHAT_%d", time.Now().Unix()),
			Subject:         "微信测试订单",
			Body:            "测试自动对账功能",
			Amount:          decimal.NewFromFloat(200.00),
			Currency:        "CNY",
			PaymentMethod:   "native",
			PaymentChannel:  "wechat",
			Status:          model.OrderStatusPaid,
			PaidAt:          &yesterday,
			ExpiredAt:       yesterday.Add(2 * time.Hour),
			CreatedAt:       yesterday,
			UpdatedAt:       yesterday,
		},
	}

	for _, order := range testOrders {
		if err := orderRepo.Create(context.Background(), order); err != nil {
			log.Printf("Failed to create test order: %v", err)
		} else {
			fmt.Printf("创建测试订单: %s (%s)\n", order.OrderNo, order.PaymentChannel)
		}
	}

	// 2. 手动触发对账（测试对账逻辑）
	fmt.Println("\n2. 手动触发对账...")
	
	// 测试支付宝对账
	fmt.Println("触发支付宝对账...")
	alipayRecord, err := reconcileService.StartReconcile(context.Background(), yesterday, "alipay")
	if err != nil {
		log.Printf("Failed to start alipay reconcile: %v", err)
	} else {
		fmt.Printf("支付宝对账已启动，批次号: %s\n", alipayRecord.BatchNo)
	}

	// 测试微信对账
	fmt.Println("触发微信对账...")
	wechatRecord, err := reconcileService.StartReconcile(context.Background(), yesterday, "wechat")
	if err != nil {
		log.Printf("Failed to start wechat reconcile: %v", err)
	} else {
		fmt.Printf("微信对账已启动，批次号: %s\n", wechatRecord.BatchNo)
	}

	// 等待对账完成
	time.Sleep(3 * time.Second)

	// 3. 查看对账结果
	fmt.Println("\n3. 查看对账结果...")
	
	if alipayRecord != nil {
		record, err := reconcileService.GetRecord(context.Background(), alipayRecord.BatchNo)
		if err != nil {
			log.Printf("Failed to get alipay reconcile record: %v", err)
		} else {
			fmt.Printf("支付宝对账结果:\n")
			fmt.Printf("  批次号: %s\n", record.BatchNo)
			fmt.Printf("  状态: %s\n", getReconcileStatusText(record.Status))
			fmt.Printf("  总订单数: %d\n", record.TotalCount)
			fmt.Printf("  总金额: %s\n", record.TotalAmount.String())
			fmt.Printf("  成功订单数: %d\n", record.SuccessCount)
			fmt.Printf("  差异订单数: %d\n", record.DiffCount)
		}
	}

	if wechatRecord != nil {
		record, err := reconcileService.GetRecord(context.Background(), wechatRecord.BatchNo)
		if err != nil {
			log.Printf("Failed to get wechat reconcile record: %v", err)
		} else {
			fmt.Printf("微信对账结果:\n")
			fmt.Printf("  批次号: %s\n", record.BatchNo)
			fmt.Printf("  状态: %s\n", getReconcileStatusText(record.Status))
			fmt.Printf("  总订单数: %d\n", record.TotalCount)
			fmt.Printf("  总金额: %s\n", record.TotalAmount.String())
			fmt.Printf("  成功订单数: %d\n", record.SuccessCount)
			fmt.Printf("  差异订单数: %d\n", record.DiffCount)
		}
	}

	// 4. 测试自动对账调度器（短时间运行）
	fmt.Println("\n4. 测试自动对账调度器...")
	
	// 临时修改配置为更频繁的执行（仅用于测试）
	fmt.Println("注意: 生产环境中对账任务每天凌晨2点自动执行")
	fmt.Println("当前配置:")
	fmt.Printf("  启用状态: %t\n", cfg.Scheduler.DailyReconcile.Enabled)
	fmt.Printf("  执行时间: %s\n", cfg.Scheduler.DailyReconcile.Cron)
	fmt.Printf("  对账渠道: %v\n", cfg.Scheduler.DailyReconcile.Channels)
	fmt.Printf("  延迟天数: %d天\n", cfg.Scheduler.DailyReconcile.DelayDays)

	// 启动调度器服务（测试5秒）
	if err := schedulerService.Start(); err != nil {
		log.Fatalf("Failed to start scheduler: %v", err)
	}

	fmt.Println("调度器已启动，等待5秒...")
	time.Sleep(5 * time.Second)

	// 停止调度器服务
	if err := schedulerService.Stop(); err != nil {
		log.Printf("Failed to stop scheduler: %v", err)
	}

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("自动对账功能已成功集成到定时任务系统中")
	fmt.Println("生产环境中将按配置的时间自动执行对账任务")
}

// getReconcileStatusText 获取对账状态文本
func getReconcileStatusText(status int8) string {
	switch status {
	case 0:
		return "处理中"
	case 1:
		return "成功"
	case 2:
		return "有差异"
	default:
		return "未知状态"
	}
}
