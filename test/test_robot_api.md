# 机器人API测试文档

## 1. 创建订单（用于测试）

```bash
curl -X POST http://localhost:8080/api/v1/payments/orders \
  -H "Content-Type: application/json" \
  -H "X-API-Key: test-merchant-key" \
  -d '{
    "merchant_order_no": "ROBOT_TEST_001",
    "subject": "机器人测试订单",
    "body": "测试机器人支付流程",
    "amount": "10.00",
    "payment_method": "native",
    "payment_channel": "alipay",
    "expire_minutes": 120
  }'
```

## 2. 机器人查询订单状态

```bash
curl -X GET http://localhost:8080/api/v1/payments/robot/orders/{order_no}
```

### 响应示例：

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "order_no": "20241214123456abcdef",
    "amount": "10.00",
    "status": 0,
    "status_text": "待支付",
    "is_paid": false,
    "is_expired": false,
    "paid_at": null,
    "expired_at": "2024-12-14T14:34:56Z",
    "payment_method": "native",
    "payment_channel": "alipay",
    "subject": "机器人测试订单"
  }
}
```

## 3. 机器人轮询逻辑示例

```javascript
// 机器人轮询查询订单状态
async function pollOrderStatus(orderNo, maxAttempts = 360, intervalMs = 10000) {
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    try {
      const response = await fetch(`/api/v1/payments/robot/orders/${orderNo}`);
      const result = await response.json();
      
      if (result.code === 200) {
        const order = result.data;
        
        // 检查订单状态
        if (order.is_paid) {
          console.log('支付成功！');
          return { success: true, order };
        }
        
        if (order.is_expired) {
          console.log('订单已过期');
          return { success: false, reason: 'expired', order };
        }
        
        // 继续等待
        console.log(`订单状态: ${order.status_text}, 继续等待...`);
      }
      
      // 等待10秒后再次查询
      await new Promise(resolve => setTimeout(resolve, intervalMs));
      
    } catch (error) {
      console.error('查询订单状态失败:', error);
    }
  }
  
  return { success: false, reason: 'timeout' };
}
```

## 4. 主要改进点

### ✅ 已完成的改进：

1. **订单过期时间调整为2小时**
   - 默认 `expire_minutes` 从 30 分钟改为 120 分钟
   - 保持了自定义过期时间的灵活性

2. **机器人专用查询接口**
   - 新增 `GET /api/v1/payments/robot/orders/{orderNo}` 接口
   - 无需商户认证，简化机器人接入
   - 返回机器人所需的关键信息：
     - `is_paid`: 是否已支付
     - `is_expired`: 是否已过期
     - `status_text`: 状态文本描述
     - 其他必要的订单信息

3. **优化的响应格式**
   - 专为机器人设计的 `RobotOrderResponse` 结构
   - 包含布尔值字段便于机器人判断状态
   - 简化了机器人的业务逻辑

### 🔄 轮询建议：

- **轮询间隔**: 建议10-30秒
- **超时时间**: 建议1小时（订单2小时过期）
- **错误处理**: 网络异常时继续重试
- **状态判断**: 优先检查 `is_paid` 和 `is_expired` 字段

## 5. 定时任务功能测试

### ✅ 已完成的定时任务功能：

**自动关闭过期订单**:
- 每5分钟自动执行一次
- 查找状态为"待支付"且已过期的订单
- 批量更新订单状态为"已关闭"
- 详细的执行日志记录

### 🧪 测试定时任务：

```bash
# 运行定时任务测试
go run test_scheduler.go
```

**测试流程**:
1. 创建一个已过期的测试订单
2. 手动执行过期订单关闭功能
3. 验证订单状态是否正确更新
4. 启动定时任务服务并观察执行情况

### 📊 定时任务监控：

**查看定时任务日志**:
```bash
# 启动服务后查看日志
./server

# 日志示例：
# INFO[2024-12-14T10:00:00Z] Scheduler service started
# INFO[2024-12-14T10:05:00Z] Starting to close expired orders
# INFO[2024-12-14T10:05:01Z] Successfully processed expired orders
```

**定时任务特性**:
- ✅ 自动启动和停止
- ✅ 优雅关闭（等待当前任务完成）
- ✅ 错误处理（单次失败不影响后续执行）
- ✅ 超时控制（单次执行最长5分钟）
- ✅ 批处理（单次最多处理100个订单）
