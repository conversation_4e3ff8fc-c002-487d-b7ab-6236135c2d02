package main

import (
	"fmt"
	"log"

	"pay-core/internal/config"
	"pay-core/pkg/payment"
)

func main() {
	fmt.Println("=== 测试关闭订单API ===")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化支付客户端
	paymentClient, err := payment.NewClient(cfg.Payment)
	if err != nil {
		log.Fatalf("Failed to create payment client: %v", err)
	}

	// 测试关闭微信订单
	fmt.Println("测试关闭微信订单...")
	wechatReq := &payment.CloseOrderRequest{
		OrderNo: "2025082309025831548a",
		Channel: "wechat",
	}

	resp, err := paymentClient.CloseOrder(wechatReq)
	if err != nil {
		fmt.Printf("关闭微信订单失败: %v\n", err)
	} else {
		fmt.Printf("微信订单关闭结果: Success=%v, Message=%s\n", resp.Success, resp.Message)
	}

	// 测试关闭支付宝订单
	fmt.Println("\n测试关闭支付宝订单...")
	alipayReq := &payment.CloseOrderRequest{
		OrderNo: "test_order_123456",
		Channel: "alipay",
	}

	resp, err = paymentClient.CloseOrder(alipayReq)
	if err != nil {
		fmt.Printf("关闭支付宝订单失败: %v\n", err)
	} else {
		fmt.Printf("支付宝订单关闭结果: Success=%v, Message=%s\n", resp.Success, resp.Message)
	}

	fmt.Println("\n测试完成")
}