package main

import (
	"fmt"
	"log"
	"os"

	"pay-core/internal/config"

	wechatv3 "github.com/go-pay/gopay/wechat/v3"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	fmt.Println("微信支付配置检查:")
	fmt.Printf("AppID: %s\n", cfg.Payment.Wechat.AppID)
	fmt.Printf("MchID: %s\n", cfg.Payment.Wechat.MchID)
	fmt.Printf("APIv3Key长度: %d\n", len(cfg.Payment.Wechat.APIv3Key))
	fmt.Printf("SerialNo: %s\n", cfg.Payment.Wechat.SerialNo)
	fmt.Printf("PrivateKeyPath: %s\n", cfg.Payment.Wechat.PrivateKeyPath)
	fmt.Printf("IsProd: %t\n", cfg.Payment.Wechat.IsProd)

	// 检查私钥文件
	if cfg.Payment.Wechat.PrivateKeyPath != "" {
		if _, err := os.Stat(cfg.Payment.Wechat.PrivateKeyPath); err != nil {
			fmt.Printf("私钥文件不存在: %v\n", err)
		} else {
			fmt.Println("私钥文件存在")
			
			// 读取私钥内容
			keyBytes, err := os.ReadFile(cfg.Payment.Wechat.PrivateKeyPath)
			if err != nil {
				fmt.Printf("读取私钥文件失败: %v\n", err)
			} else {
				fmt.Printf("私钥文件大小: %d bytes\n", len(keyBytes))
			}
		}
	}

	// 尝试创建微信支付客户端
	fmt.Println("\n尝试创建微信支付V3客户端...")
	
	var privateKeyContent string
	if cfg.Payment.Wechat.PrivateKeyPath != "" {
		keyBytes, err := os.ReadFile(cfg.Payment.Wechat.PrivateKeyPath)
		if err != nil {
			log.Fatalf("Failed to read private key: %v", err)
		}
		privateKeyContent = string(keyBytes)
	}

	client, err := wechatv3.NewClientV3(
		cfg.Payment.Wechat.MchID,
		cfg.Payment.Wechat.SerialNo,
		cfg.Payment.Wechat.APIv3Key,
		privateKeyContent,
	)
	
	if err != nil {
		fmt.Printf("创建微信支付客户端失败: %v\n", err)
	} else {
		fmt.Println("微信支付客户端创建成功!")
		
		// 设置生产环境标志
		if cfg.Payment.Wechat.IsProd {
			client.SetBodySize(1024 * 1024) // 设置为生产环境
		} else {
			// 测试环境不需要特殊设置
			fmt.Println("使用测试环境配置")
		}
	}
}