package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"pay-core/internal/config"
	"pay-core/internal/handler"
	"pay-core/internal/model"
	"pay-core/internal/repository"
	"pay-core/internal/service"
	"pay-core/pkg/database"
	"pay-core/pkg/payment"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"
)

// PaymentIntegrationTestSuite 支付集成测试套件
type PaymentIntegrationTestSuite struct {
	suite.Suite
	db            *gorm.DB
	router        *gin.Engine
	paymentSvc    service.PaymentService
	orderRepo     repository.PaymentOrderRepository
	refundRepo    repository.RefundOrderRepository
	recordRepo    repository.PaymentRecordRepository
	appRepo       repository.AppRepository
	paymentClient *payment.Client
}

// SetupSuite 设置测试套件
func (suite *PaymentIntegrationTestSuite) SetupSuite() {
	// 初始化测试数据库
	db, err := database.InitTestDB()
	require.NoError(suite.T(), err)
	suite.db = db

	// 初始化仓储
	suite.orderRepo = repository.NewPaymentOrderRepository(db)
	suite.refundRepo = repository.NewRefundOrderRepository(db)
	suite.recordRepo = repository.NewPaymentRecordRepository(db)
	suite.appRepo = repository.NewAppRepository(db)

	// 初始化支付客户端（使用测试配置）
	paymentConfig := config.PaymentConfig{
		Alipay: config.AlipayConfig{
			AppID:      "test_app_id",
			PrivateKey: "test_private_key",
			PublicKey:  "test_public_key",
			IsProd:     false,
		},
		Wechat: config.WechatConfig{
			AppID:  "test_app_id",
			MchID:  "test_mch_id",
			APIKey: "test_api_key",
			IsProd: false,
		},
	}

	suite.paymentClient, err = payment.NewClient(paymentConfig)
	require.NoError(suite.T(), err)

	// 初始化服务
	suite.paymentSvc = service.NewPaymentService(
		suite.orderRepo,
		suite.refundRepo,
		suite.recordRepo,
		suite.appRepo,
		suite.paymentClient,
		nil, // redis client
	)

	// 初始化路由
	gin.SetMode(gin.TestMode)
	suite.router = gin.New()

	// 注册路由
	paymentHandler := handler.NewPaymentHandler(suite.paymentSvc)
	v1 := suite.router.Group("/api/v1")
	{
		v1.POST("/orders", paymentHandler.CreateOrder)
		v1.GET("/orders/:order_no", paymentHandler.GetOrder)
		v1.POST("/notify/:channel", paymentHandler.HandleNotify)
	}
}

// TearDownSuite 清理测试套件
func (suite *PaymentIntegrationTestSuite) TearDownSuite() {
	if suite.db != nil {
		// 清理测试数据
		suite.db.Exec("DROP TABLE IF EXISTS payment_orders")
		suite.db.Exec("DROP TABLE IF EXISTS payment_records")

		// 关闭数据库连接
		sqlDB, _ := suite.db.DB()
		sqlDB.Close()
	}
}

// SetupTest 每个测试前的设置
func (suite *PaymentIntegrationTestSuite) SetupTest() {
	// 清理测试数据
	suite.db.Exec("DELETE FROM payment_orders")
	suite.db.Exec("DELETE FROM payment_records")
}

// MockPaymentClient 模拟支付客户端
type MockPaymentClient struct{}

func (m *MockPaymentClient) CreateNativeOrder(req *payment.NativeOrderRequest) (*payment.NativeOrderResponse, error) {
	return &payment.NativeOrderResponse{
		QRCode:   fmt.Sprintf("https://example.com/qr/%s", req.OrderNo),
		PrepayID: fmt.Sprintf("prepay_%s", req.OrderNo),
		OrderNo:  req.OrderNo,
		TradeNo:  fmt.Sprintf("trade_%s", req.OrderNo),
	}, nil
}

func (m *MockPaymentClient) QueryOrder(req *payment.QueryOrderRequest) (*payment.QueryOrderResponse, error) {
	return &payment.QueryOrderResponse{
		OrderNo:       req.OrderNo,
		TransactionID: fmt.Sprintf("tx_%s", req.OrderNo),
		Amount:        decimal.NewFromFloat(100.00),
		Status:        "SUCCESS",
		PaidAt:        time.Now().Format("20060102150405"),
	}, nil
}

func (m *MockPaymentClient) ParseNotify(ctx context.Context, channel string, req *http.Request) (*payment.NotifyData, error) {
	// 从请求中解析订单号（简化实现）
	orderNo := req.FormValue("out_trade_no")
	if orderNo == "" {
		orderNo = "test_order_001"
	}

	return &payment.NotifyData{
		Type:          payment.NotifyTypePaid,
		OrderNo:       orderNo,
		TransactionID: fmt.Sprintf("tx_%s", orderNo),
		Amount:        decimal.NewFromFloat(100.00),
		Status:        "SUCCESS",
		PaidAt:        time.Now().Format("20060102150405"),
	}, nil
}

// TestCreateOrder 测试创建订单的完整流程
func (suite *PaymentIntegrationTestSuite) TestCreateOrder() {
	// 准备请求数据
	reqData := map[string]interface{}{
		"app_id":         1,
		"app_order_no":   "test_order_001",
		"subject":        "Test Product",
		"body":           "Test Product Description",
		"total_amount":   "100.00",
		"pay_channel":    "wechat",
		"expire_minutes": 30,
		"notify_url":     "http://example.com/notify",
	}

	jsonData, _ := json.Marshal(reqData)
	req := httptest.NewRequest("POST", "/api/v1/orders", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var resp map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &resp)
	require.NoError(suite.T(), err)

	assert.Equal(suite.T(), float64(0), resp["code"])
	assert.NotNil(suite.T(), resp["data"])

	data := resp["data"].(map[string]interface{})
	assert.NotEmpty(suite.T(), data["order_no"])
	assert.Equal(suite.T(), "100", data["total_amount"])
	assert.Equal(suite.T(), "wechat", data["pay_channel"])
	assert.NotNil(suite.T(), data["pay_info"])

	// 验证数据库中的订单
	var order model.PaymentOrder
	err = suite.db.Where("app_order_no = ?", "test_order_001").First(&order).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), model.OrderStatusWaitingPay, order.Status)
}

// TestGetOrder 测试查询订单
func (suite *PaymentIntegrationTestSuite) TestGetOrder() {
	// 先创建一个订单
	order := &model.PaymentOrder{
		AppID:       1,
		AppOrderNo:  "test_order_002",
		OrderNo:     "ORDER_20231201_002",
		Subject:     "Test Product",
		Body:        "Test Product Description",
		TotalAmount: decimal.NewFromFloat(100.00),
		PayChannel:  "alipay",
		Status:      model.OrderStatusWaitingPay,
		ExpireTime:  time.Now().Add(30 * time.Minute),
	}

	err := suite.db.Create(order).Error
	require.NoError(suite.T(), err)

	// 查询订单
	req := httptest.NewRequest("GET", "/api/v1/orders/ORDER_20231201_002", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var resp map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &resp)
	require.NoError(suite.T(), err)

	assert.Equal(suite.T(), float64(0), resp["code"])
	data := resp["data"].(map[string]interface{})
	assert.Equal(suite.T(), "ORDER_20231201_002", data["order_no"])
	assert.Equal(suite.T(), "PENDING", data["status"])
}

// TestHandleNotify 测试处理支付回调
func (suite *PaymentIntegrationTestSuite) TestHandleNotify() {
	// 先创建一个订单
	order := &model.PaymentOrder{
		AppID:       1,
		AppOrderNo:  "test_order_003",
		OrderNo:     "ORDER_20231201_003",
		Subject:     "Test Product",
		Body:        "Test Product Description",
		TotalAmount: decimal.NewFromFloat(100.00),
		PayChannel:  "wechat",
		Status:      model.OrderStatusWaitingPay,
		ExpireTime:  time.Now().Add(30 * time.Minute),
	}

	err := suite.db.Create(order).Error
	require.NoError(suite.T(), err)

	// 模拟支付回调
	notifyData := "out_trade_no=ORDER_20231201_003&transaction_id=wx_123&total_fee=10000"
	req := httptest.NewRequest("POST", "/api/v1/notify/wechat", bytes.NewBufferString(notifyData))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	// 验证订单状态已更新
	var updatedOrder model.PaymentOrder
	err = suite.db.Where("order_no = ?", "ORDER_20231201_003").First(&updatedOrder).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), model.OrderStatusPaid, updatedOrder.Status)
	assert.NotNil(suite.T(), updatedOrder.PaidAt)

	// 验证支付记录已创建
	var paymentRecord model.PaymentRecord
	err = suite.db.Where("order_no = ?", "ORDER_20231201_003").First(&paymentRecord).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), "wechat", paymentRecord.PayChannel)
}

// TestPaymentIntegrationSuite 运行集成测试套件
func TestPaymentIntegrationSuite(t *testing.T) {
	suite.Run(t, new(PaymentIntegrationTestSuite))
}
