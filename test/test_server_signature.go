package main

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
)

func main() {
	// 模拟服务器端的签名验证逻辑
	appSecret := "7Hk9pJ2fTdR8sLq4wVbN5zKmG6xYcX3F"
	
	// 模拟请求体
	requestBody := `{"amount":100.5,"app_user_id":"user_123","app_order_no":"ORDER_TEST_123","subject":"测试商品","pay_channel":"wechat"}`
	
	// 认证参数
	clientID := "robot_user123"
	timestamp := "1755872000"
	nonce := "test_nonce_123"
	
	// 解析参数
	params := make(map[string]string)
	
	// 解析JSON参数
	var jsonParams map[string]interface{}
	if err := json.Unmarshal([]byte(requestBody), &jsonParams); err == nil {
		for key, value := range jsonParams {
			params[key] = fmt.Sprintf("%v", value)
		}
	}
	
	// 添加认证参数
	params["api_key"] = clientID
	params["timestamp"] = timestamp
	params["nonce"] = nonce
	
	// 生成签名
	signature := generateSignature(params, appSecret)
	
	fmt.Println("=== 服务器端签名验证 ===")
	fmt.Printf("Request Body: %s\n", requestBody)
	fmt.Printf("Client ID: %s\n", clientID)
	fmt.Printf("Timestamp: %s\n", timestamp)
	fmt.Printf("Nonce: %s\n", nonce)
	fmt.Printf("App Secret: %s\n", appSecret)
	
	fmt.Println("\n解析的参数:")
	for key, value := range params {
		fmt.Printf("  %s = %s\n", key, value)
	}
	
	// 构建签名字符串
	signString := buildSignString(params, appSecret)
	fmt.Printf("\nSign String: %s\n", signString)
	fmt.Printf("Signature: %s\n", signature)
}

func generateSignature(params map[string]string, appSecret string) string {
	// 1. 获取所有键并排序
	keys := make([]string, 0, len(params))
	for key := range params {
		if key != "signature" && params[key] != "" {
			keys = append(keys, key)
		}
	}
	sort.Strings(keys)

	// 2. 构建签名字符串
	var parts []string
	for _, key := range keys {
		parts = append(parts, fmt.Sprintf("%s=%s", key, params[key]))
	}

	signStr := strings.Join(parts, "&") + "&key=" + appSecret

	// 3. 计算HMAC-SHA256
	h := hmac.New(sha256.New, []byte(appSecret))
	h.Write([]byte(signStr))

	return hex.EncodeToString(h.Sum(nil))
}

func buildSignString(params map[string]string, appSecret string) string {
	// 1. 获取所有键并排序
	keys := make([]string, 0, len(params))
	for key := range params {
		if key != "signature" && params[key] != "" {
			keys = append(keys, key)
		}
	}
	sort.Strings(keys)

	// 2. 构建签名字符串
	var parts []string
	for _, key := range keys {
		parts = append(parts, fmt.Sprintf("%s=%s", key, params[key]))
	}

	return strings.Join(parts, "&") + "&key=" + appSecret
}