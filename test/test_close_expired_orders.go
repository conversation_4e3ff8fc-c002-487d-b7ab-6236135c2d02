package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"pay-core/internal/config"
	"pay-core/internal/model"
	"pay-core/internal/repository"
	"pay-core/internal/service"
	"pay-core/pkg/database"
	"pay-core/pkg/logger"
	"pay-core/pkg/payment"
	"pay-core/pkg/types"

	"github.com/shopspring/decimal"
)

func main() {
	// 初始化日志
	logger.Init()

	// 加载配置
	cfg, err := config.Load("../configs/config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库
	db, err := database.NewMySQL(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 初始化支付客户端
	paymentClient, err := payment.NewClient(cfg.Payment)
	if err != nil {
		log.Fatalf("Failed to create payment client: %v", err)
	}

	// 初始化仓库
	appRepo := repository.NewAppRepository(db)
	paymentOrderRepo := repository.NewPaymentOrderRepository(db)

	// 初始化服务
	paymentService := service.NewPaymentService(
		paymentOrderRepo,
		nil, // refundOrderRepo
		appRepo,
		paymentClient,
		nil, // redis
	)

	ctx := context.Background()

	// 测试1: 创建一个即将过期的订单
	fmt.Println("=== 测试关闭过期订单功能 ===")

	// 创建测试订单
	req := &model.CreateOrderRequest{
		MerchantOrderNo: fmt.Sprintf("test_close_%d", time.Now().Unix()),
		TotalAmount:     decimal.NewFromFloat(0.01),
		Subject:         "测试关闭过期订单",
		Body:            "测试订单描述",
		PaymentChannel:  "wechat",
		ExpireMinutes:   1, // 1分钟后过期
	}

	// 假设使用测试应用ID
	appID := uint64(1)

	resp, err := paymentService.CreateOrder(ctx, appID, req)
	if err != nil {
		log.Printf("创建订单失败: %v", err)
		return
	}

	fmt.Printf("创建订单成功: %s\n", resp.OrderNo)
	fmt.Printf("过期时间: %s\n", resp.ExpireTime.Format("2006-01-02 15:04:05"))

	// 等待订单过期
	fmt.Println("等待订单过期...")
	time.Sleep(65 * time.Second) // 等待65秒确保订单过期

	// 测试关闭过期订单
	fmt.Println("开始关闭过期订单...")
	err = paymentService.CloseExpiredOrders(ctx)
	if err != nil {
		log.Printf("关闭过期订单失败: %v", err)
	} else {
		fmt.Println("关闭过期订单完成")
	}

	// 查询订单状态确认
	order, err := paymentService.GetOrder(ctx, appID, resp.OrderNo)
	if err != nil {
		log.Printf("查询订单失败: %v", err)
		return
	}

	fmt.Printf("订单最终状态: %s\n", order.Status)

	// 测试2: 测试直接关闭支付平台订单
	fmt.Println("\n=== 测试直接关闭支付平台订单 ===")

	closeReq := &payment.CloseOrderRequest{
		OrderNo: resp.OrderNo,
		Channel: "wechat",
	}

	closeResp, err := paymentClient.CloseOrder(closeReq)
	if err != nil {
		log.Printf("关闭支付平台订单失败: %v", err)
	} else {
		fmt.Printf("关闭支付平台订单结果: Success=%v, Message=%s\n", 
			closeResp.Success, closeResp.Message)
	}

	fmt.Println("测试完成")
}