package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"pay-core/internal/config"
	"pay-core/internal/model"
	"pay-core/internal/repository"
	"pay-core/internal/service"
	"pay-core/pkg/database"
	"pay-core/pkg/logger"
	"pay-core/pkg/payment"

	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
)

// 测试定时任务功能
func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logger.Init(cfg.Log)

	// 初始化数据库
	db, err := database.NewMySQL(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 初始化Redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     cfg.Redis.Addr,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})

	// 初始化支付客户端
	paymentClient, err := payment.NewClient(cfg.Payment)
	if err != nil {
		log.Fatalf("Failed to initialize payment client: %v", err)
	}

	// 初始化Repository和Service
	merchantRepo := repository.NewMerchantRepository(db)
	orderRepo := repository.NewOrderRepository(db)
	reconcileRepo := repository.NewReconcileRepository(db)
	paymentService := service.NewPaymentService(orderRepo, merchantRepo, paymentClient, rdb)
	reconcileService := service.NewReconcileService(reconcileRepo, orderRepo, paymentClient)
	schedulerService := service.NewSchedulerService(paymentService, reconcileService, rdb, cfg.Scheduler)

	fmt.Println("=== 定时任务测试 ===")

	// 1. 创建一个已过期的测试订单
	fmt.Println("1. 创建测试订单...")
	testOrder := &model.PaymentOrder{
		OrderNo:         fmt.Sprintf("TEST_%d", time.Now().Unix()),
		MerchantID:      1,
		MerchantOrderNo: fmt.Sprintf("MERCHANT_TEST_%d", time.Now().Unix()),
		Subject:         "定时任务测试订单",
		Body:            "测试过期订单关闭功能",
		Amount:          decimal.NewFromFloat(10.00),
		Currency:        "CNY",
		PaymentMethod:   "native",
		PaymentChannel:  "alipay",
		Status:          model.OrderStatusPending,
		ExpiredAt:       time.Now().Add(-1 * time.Hour), // 设置为1小时前过期
		NotifyURL:       "http://localhost:8080/test/notify",
		ReturnURL:       "http://localhost:8080/test/return",
	}

	if err := orderRepo.Create(context.Background(), testOrder); err != nil {
		log.Fatalf("Failed to create test order: %v", err)
	}
	fmt.Printf("创建测试订单成功: %s (已过期)\n", testOrder.OrderNo)

	// 2. 手动执行一次过期订单关闭
	fmt.Println("\n2. 手动执行过期订单关闭...")
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	err = paymentService.CloseExpiredOrders(ctx)
	if err != nil {
		log.Fatalf("Failed to close expired orders: %v", err)
	}
	fmt.Println("手动执行完成")

	// 3. 检查订单状态
	fmt.Println("\n3. 检查订单状态...")
	updatedOrder, err := orderRepo.GetByOrderNo(context.Background(), testOrder.OrderNo)
	if err != nil {
		log.Fatalf("Failed to get updated order: %v", err)
	}

	fmt.Printf("订单状态: %s -> %s\n",
		getStatusText(testOrder.Status),
		getStatusText(updatedOrder.Status))

	if updatedOrder.Status == model.OrderStatusClosed {
		fmt.Println("✅ 过期订单关闭功能正常")
	} else {
		fmt.Println("❌ 过期订单关闭功能异常")
	}

	// 4. 启动定时任务服务（测试10秒）
	fmt.Println("\n4. 启动定时任务服务（测试10秒）...")
	if err := schedulerService.Start(); err != nil {
		log.Fatalf("Failed to start scheduler: %v", err)
	}

	// 等待10秒观察定时任务执行
	time.Sleep(10 * time.Second)

	// 5. 停止定时任务服务
	fmt.Println("\n5. 停止定时任务服务...")
	if err := schedulerService.Stop(); err != nil {
		log.Printf("Failed to stop scheduler: %v", err)
	}

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("注意: 定时任务每5分钟执行一次，在实际运行中会自动处理过期订单")
}

// getStatusText 获取状态文本
func getStatusText(status int8) string {
	switch status {
	case model.OrderStatusPending:
		return "待支付"
	case model.OrderStatusPaid:
		return "支付成功"
	case model.OrderStatusFailed:
		return "支付失败"
	case model.OrderStatusClosed:
		return "已关闭"
	case model.OrderStatusRefunded:
		return "已退款"
	case model.OrderStatusRefunding:
		return "退款中"
	default:
		return "未知状态"
	}
}
