package main

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
)

func main() {
	// 测试参数
	clientID := "robot_001"
	appSecret := "7Hk9pJ2fTdR8sLq4wVbN5zKmG6xYcX3F"
	baseURL := "http://localhost:8080"

	// 测试创建订单
	testCreateOrder(clientID, appSecret, baseURL)
	
	// 测试查询订单
	testQueryOrder(clientID, appSecret, baseURL)
	
	// 测试错误情况
	testInvalidSignature(clientID, appSecret, baseURL)
}

func testCreateOrder(clientID, appSecret, baseURL string) {
	fmt.Println("=== 测试创建订单 ===")
	
	// 请求数据
	orderData := map[string]interface{}{
		"app_order_no": fmt.Sprintf("TEST_ORDER_%d", time.Now().Unix()),
		"amount":       100.50,
		"subject":      "测试商品",
		"body":         "这是一个测试订单",
		"pay_channel":  "alipay",
		"app_user_id":  "user_123",
	}

	resp, err := sendRequest("POST", baseURL+"/api/v1/orders", orderData, clientID, appSecret)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}

	fmt.Printf("响应: %s\n\n", resp)
}

func testQueryOrder(clientID, appSecret, baseURL string) {
	fmt.Println("=== 测试查询订单 ===")
	
	resp, err := sendRequest("GET", baseURL+"/api/v1/orders/query?app_order_no=TEST123", nil, clientID, appSecret)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}

	fmt.Printf("响应: %s\n\n", resp)
}

func testInvalidSignature(clientID, appSecret, baseURL string) {
	fmt.Println("=== 测试无效签名 ===")
	
	// 使用错误的密钥
	wrongSecret := "wrong_secret"
	resp, err := sendRequest("GET", baseURL+"/api/v1/orders/query", nil, clientID, wrongSecret)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}

	fmt.Printf("响应: %s\n\n", resp)
}

func sendRequest(method, url string, data interface{}, clientID, appSecret string) (string, error) {
	// 准备请求体
	var reqBody []byte
	var err error
	if data != nil {
		reqBody, err = json.Marshal(data)
		if err != nil {
			return "", err
		}
	}

	// 创建请求
	req, err := http.NewRequest(method, url, bytes.NewBuffer(reqBody))
	if err != nil {
		return "", err
	}

	if data != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// 生成认证参数
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := generateNonce()
	signature := generateSignature(method, url, reqBody, clientID, timestamp, nonce, appSecret)

	// 设置认证头
	req.Header.Set("X-API-Key", clientID)
	req.Header.Set("X-Timestamp", timestamp)
	req.Header.Set("X-Nonce", nonce)
	req.Header.Set("X-Signature", signature)

	// 打印请求信息
	fmt.Printf("请求: %s %s\n", method, url)
	fmt.Printf("X-API-Key: %s\n", clientID)
	fmt.Printf("X-Timestamp: %s\n", timestamp)
	fmt.Printf("X-Nonce: %s\n", nonce)
	fmt.Printf("X-Signature: %s\n", signature)
	if len(reqBody) > 0 {
		fmt.Printf("Body: %s\n", string(reqBody))
	}

	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return string(respBody), nil
}

func generateNonce() string {
	return fmt.Sprintf("nonce_%d", time.Now().UnixNano())
}

func generateSignature(method, url string, body []byte, clientID, timestamp, nonce, appSecret string) string {
	params := make(map[string]string)

	// 解析URL参数
	if strings.Contains(url, "?") {
		parts := strings.Split(url, "?")
		if len(parts) > 1 {
			queryParams := strings.Split(parts[1], "&")
			for _, param := range queryParams {
				kv := strings.Split(param, "=")
				if len(kv) == 2 {
					params[kv[0]] = kv[1]
				}
			}
		}
	}

	// 解析JSON参数
	if len(body) > 0 {
		var jsonParams map[string]interface{}
		if err := json.Unmarshal(body, &jsonParams); err == nil {
			for key, value := range jsonParams {
				params[key] = fmt.Sprintf("%v", value)
			}
		}
	}

	// 添加认证参数
	params["api_key"] = clientID
	params["timestamp"] = timestamp
	params["nonce"] = nonce

	// 生成签名
	return calculateSignature(params, appSecret)
}

func calculateSignature(params map[string]string, appSecret string) string {
	// 1. 获取所有键并排序
	keys := make([]string, 0, len(params))
	for key := range params {
		if key != "signature" && params[key] != "" {
			keys = append(keys, key)
		}
	}
	sort.Strings(keys)

	// 2. 构建签名字符串
	var parts []string
	for _, key := range keys {
		parts = append(parts, fmt.Sprintf("%s=%s", key, params[key]))
	}

	signStr := strings.Join(parts, "&") + "&key=" + appSecret
	fmt.Printf("签名字符串: %s\n", signStr)

	// 3. 计算HMAC-SHA256
	h := hmac.New(sha256.New, []byte(appSecret))
	h.Write([]byte(signStr))

	return hex.EncodeToString(h.Sum(nil))
}