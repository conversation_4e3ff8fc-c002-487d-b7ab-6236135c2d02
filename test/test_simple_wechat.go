package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"pay-core/internal/config"

	"github.com/go-pay/gopay"
	wechatv3 "github.com/go-pay/gopay/wechat/v3"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 读取私钥
	keyBytes, err := os.ReadFile(cfg.Payment.Wechat.PrivateKeyPath)
	if err != nil {
		log.Fatalf("Failed to read private key: %v", err)
	}
	privateKeyContent := string(keyBytes)

	// 创建微信支付客户端
	client, err := wechatv3.NewClientV3(
		cfg.Payment.Wechat.MchID,
		cfg.Payment.Wechat.SerialNo,
		cfg.Payment.Wechat.APIv3Key,
		privateKeyContent,
	)
	if err != nil {
		log.Fatalf("Failed to create wechat client: %v", err)
	}

	// 设置调试模式
	client.DebugSwitch = gopay.DebugOn

	fmt.Println("开始测试微信支付V3 API...")

	// 构建最简单的订单参数
	orderNo := fmt.Sprintf("TEST_%d", time.Now().Unix())

	bm := make(gopay.BodyMap)
	bm.Set("appid", cfg.Payment.Wechat.AppID).
		Set("mchid", cfg.Payment.Wechat.MchID).
		Set("description", "测试").
		Set("out_trade_no", orderNo).
		Set("notify_url", "https://example.com/notify").
		SetBodyMap("amount", func(bm gopay.BodyMap) {
			bm.Set("total", 1). // 1分钱
				Set("currency", "CNY")
		})

	fmt.Printf("订单号: %s\n", orderNo)
	fmt.Printf("请求参数: %+v\n", bm)

	// 调用API
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	fmt.Println("正在调用微信支付V3 Native API...")
	
	wxRsp, err := client.V3TransactionNative(ctx, bm)
	
	fmt.Printf("调用完成，错误: %v\n", err)
	
	if err != nil {
		fmt.Printf("详细错误信息: %v\n", err)
		return
	}

	fmt.Printf("响应状态码: %d\n", wxRsp.Code)
	fmt.Printf("响应错误: %s\n", wxRsp.Error)
	
	if wxRsp.Response != nil {
		fmt.Printf("响应数据: %+v\n", wxRsp.Response)
	}
}