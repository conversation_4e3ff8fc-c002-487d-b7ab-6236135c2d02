package main

import (
	"fmt"
	"log"

	"pay-core/internal/config"
	"pay-core/pkg/payment"
)

func main() {
	fmt.Println("=== 详细测试关闭订单功能 ===")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 打印配置信息
	fmt.Printf("微信配置 - AppID: %s, MchID: %s\n", cfg.Payment.Wechat.AppID, cfg.Payment.Wechat.MchID)
	fmt.Printf("支付宝配置 - AppID: %s\n", cfg.Payment.Alipay.AppID)

	// 初始化支付客户端
	paymentClient, err := payment.NewClient(cfg.Payment)
	if err != nil {
		log.Fatalf("Failed to create payment client: %v", err)
	}

	// 测试订单号
	testOrderNo := "2025082309025831548a"
	fmt.Printf("测试订单号: %s\n", testOrderNo)

	// 测试关闭微信订单
	fmt.Println("\n=== 测试关闭微信订单 ===")
	wechatReq := &payment.CloseOrderRequest{
		OrderNo: testOrderNo,
		Channel: "wechat",
	}

	resp, err := paymentClient.CloseOrder(wechatReq)
	if err != nil {
		fmt.Printf("❌ 关闭微信订单失败: %v\n", err)
	} else {
		if resp.Success {
			fmt.Printf("✅ 微信订单关闭成功: %s\n", resp.Message)
		} else {
			fmt.Printf("⚠️  微信订单关闭失败: %s\n", resp.Message)
		}
	}

	// 测试关闭支付宝订单（如果配置了）
	if cfg.Payment.Alipay.AppID != "" {
		fmt.Println("\n=== 测试关闭支付宝订单 ===")
		alipayReq := &payment.CloseOrderRequest{
			OrderNo: testOrderNo,
			Channel: "alipay",
		}

		resp, err = paymentClient.CloseOrder(alipayReq)
		if err != nil {
			fmt.Printf("❌ 关闭支付宝订单失败: %v\n", err)
		} else {
			if resp.Success {
				fmt.Printf("✅ 支付宝订单关闭成功: %s\n", resp.Message)
			} else {
				fmt.Printf("⚠️  支付宝订单关闭失败: %s\n", resp.Message)
			}
		}
	} else {
		fmt.Println("\n⚠️  支付宝未配置，跳过测试")
	}

	// 测试不支持的支付渠道
	fmt.Println("\n=== 测试不支持的支付渠道 ===")
	unsupportedReq := &payment.CloseOrderRequest{
		OrderNo: testOrderNo,
		Channel: "unknown",
	}

	resp, err = paymentClient.CloseOrder(unsupportedReq)
	if err != nil {
		fmt.Printf("✅ 预期错误: %v\n", err)
	} else {
		fmt.Printf("❌ 应该返回错误，但返回了: %+v\n", resp)
	}

	fmt.Println("\n=== 测试完成 ===")
}