package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"pay-core/internal/config"
	"pay-core/internal/model"
	"pay-core/pkg/database"
	"pay-core/pkg/robot"
	"pay-core/pkg/types"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db, err := database.NewMySQL(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 创建机器人客户端
	robotClient := robot.NewClient(robot.Config{
		BaseURL:   cfg.Robot.BaseURL,
		SharedKey: cfg.Robot.SharedKey,
		Timeout:   cfg.Robot.Timeout,
	})

	fmt.Println("=== 机器人账户服务回调测试 ===")

	// 1. 测试API Key生成
	testAPIKeyGeneration(robotClient)

	// 2. 创建测试订单
	testOrder := createTestOrder(db)
	fmt.Printf("创建测试订单: %s\n", testOrder.OrderNo)

	// 3. 测试机器人服务充值调用
	testRobotRecharge(robotClient, testOrder)

	fmt.Println("=== 机器人账户服务回调测试完成 ===")
}

// testAPIKeyGeneration 测试API Key生成
func testAPIKeyGeneration(client *robot.Client) {
	fmt.Println("\n--- 测试API Key生成 ---")

	deviceSN := "100000064787"
	
	// 构建测试充值请求
	rechargeReq := &robot.RechargeRequest{
		Amount:      decimal.NewFromFloat(100.00),
		OrderID:     "TEST_ORDER_123",
		Description: "测试充值",
	}

	// 获取请求信息
	requestInfo, requestBody, err := client.GetRequestInfo(deviceSN, rechargeReq)
	if err != nil {
		fmt.Printf("生成API Key失败: %v\n", err)
		return
	}

	fmt.Printf("设备SN: %s\n", deviceSN)
	fmt.Printf("请求体: %s\n", requestBody)
	fmt.Printf("完整请求信息:\n%s\n", requestInfo)
}

// createTestOrder 创建测试订单
func createTestOrder(db *gorm.DB) *model.PaymentOrder {
	orderNo := fmt.Sprintf("TEST_ROBOT_%d", time.Now().Unix())
	
	order := &model.PaymentOrder{
		OrderNo:     orderNo,
		AppID:       1, // 假设存在ID为1的应用
		AppOrderNo:  fmt.Sprintf("APP_%d", time.Now().Unix()),
		AppUserID:   "100000064787", // 设备SN作为AppUserID
		TotalAmount: decimal.NewFromFloat(100.00),
		Subject:     "机器人服务回调测试订单",
		Body:        "这是一个用于测试机器人服务回调的订单",
		PayChannel:  "wechat",
		Status:      model.OrderStatusPaid, // 设置为已支付状态
		OutTradeNo:  fmt.Sprintf("wx_%d", time.Now().Unix()),
		PaidAt:      &types.Time{Time: time.Now()},
		ExpireTime:  types.Time{Time: time.Now().Add(30 * time.Minute)},
		PayerInfo: model.JSON{
			"transaction_id": fmt.Sprintf("wx_trans_%d", time.Now().Unix()),
		},
	}

	if err := db.Create(order).Error; err != nil {
		log.Fatalf("Failed to create test order: %v", err)
	}

	return order
}

// testRobotRecharge 测试机器人服务充值
func testRobotRecharge(client *robot.Client, order *model.PaymentOrder) {
	fmt.Println("\n--- 测试机器人服务充值 ---")

	deviceSN := order.AppUserID
	if deviceSN == "" {
		fmt.Println("错误：订单中没有AppUserID（设备SN）")
		return
	}

	// 构建充值请求
	rechargeReq := &robot.RechargeRequest{
		Amount:      order.TotalAmount,
		OrderID:     order.OrderNo,
		Description: "账户充值",
	}

	fmt.Printf("充值请求参数:\n")
	fmt.Printf("  设备SN: %s\n", deviceSN)
	fmt.Printf("  订单号: %s\n", rechargeReq.OrderID)
	fmt.Printf("  充值金额: %s\n", rechargeReq.Amount.String())
	fmt.Printf("  描述: %s\n", rechargeReq.Description)

	// 调用机器人服务
	ctx := context.Background()
	resp, err := client.Recharge(ctx, deviceSN, rechargeReq)
	if err != nil {
		fmt.Printf("机器人服务调用失败: %v\n", err)
		fmt.Println("注意：这可能是预期的错误，因为测试环境可能无法连接到真实的机器人服务")
		return
	}

	fmt.Printf("机器人服务调用成功:\n")
	fmt.Printf("  响应码: %d\n", resp.Code)
	fmt.Printf("  响应消息: %s\n", resp.Message)
	if resp.Data.Success {
		fmt.Printf("  充值成功: %t\n", resp.Data.Success)
		fmt.Printf("  订单号: %s\n", resp.Data.OrderID)
	}
}

// 示例：验证API Key生成算法
func verifyAPIKeyAlgorithm() {
	fmt.Println("\n--- 验证API Key生成算法 ---")
	
	// 参考Java代码的示例
	deviceSN := "100000064787"
	sharedKey := "default-shared-key-change-in-production"
	
	// 创建临时客户端用于测试
	client := robot.NewClient(robot.Config{
		BaseURL:   "http://test.example.com",
		SharedKey: sharedKey,
		Timeout:   30,
	})
	
	// 构建测试请求
	rechargeReq := &robot.RechargeRequest{
		Amount:      decimal.NewFromFloat(100.00),
		OrderID:     "TEST_ORDER",
		Description: "测试",
	}
	
	// 生成API Key
	requestInfo, _, err := client.GetRequestInfo(deviceSN, rechargeReq)
	if err != nil {
		fmt.Printf("生成API Key失败: %v\n", err)
		return
	}
	
	fmt.Printf("设备SN: %s\n", deviceSN)
	fmt.Printf("共享密钥: %s\n", sharedKey)
	fmt.Printf("生成的请求信息:\n%s\n", requestInfo)
	
	fmt.Println("算法验证:")
	fmt.Println("1. 使用设备SN作为payload")
	fmt.Println("2. 使用HMAC-SHA256计算签名")
	fmt.Println("3. 组合payload:signature")
	fmt.Println("4. Base64 URL编码")
}
