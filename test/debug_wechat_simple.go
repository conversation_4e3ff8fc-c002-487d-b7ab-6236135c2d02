package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"pay-core/internal/config"

	wechatv3 "github.com/go-pay/gopay/wechat/v3"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	fmt.Printf("配置信息:\n")
	fmt.Printf("AppID: %s\n", cfg.Payment.Wechat.AppID)
	fmt.Printf("MchID: %s\n", cfg.Payment.Wechat.MchID)
	fmt.Printf("SerialNo: %s\n", cfg.Payment.Wechat.SerialNo)
	fmt.Printf("APIv3Key: %s\n", cfg.Payment.Wechat.APIv3Key[:8]+"...")
	fmt.Printf("PrivateKeyPath: %s\n", cfg.Payment.Wechat.PrivateKeyPath)
	fmt.Printf("IsProd: %v\n", cfg.Payment.Wechat.IsProd)

	// 检查私钥文件
	if _, err := os.Stat(cfg.Payment.Wechat.PrivateKeyPath); os.IsNotExist(err) {
		log.Fatalf("Private key file does not exist: %s", cfg.Payment.Wechat.PrivateKeyPath)
	}

	// 读取私钥
	keyBytes, err := os.ReadFile(cfg.Payment.Wechat.PrivateKeyPath)
	if err != nil {
		log.Fatalf("Failed to read private key: %v", err)
	}
	privateKeyContent := string(keyBytes)
	fmt.Printf("Private key loaded, length: %d\n", len(privateKeyContent))

	// 创建微信支付客户端
	client, err := wechatv3.NewClientV3(
		cfg.Payment.Wechat.MchID,
		cfg.Payment.Wechat.SerialNo,
		cfg.Payment.Wechat.APIv3Key,
		privateKeyContent,
	)
	if err != nil {
		log.Fatalf("Failed to create wechat client: %v", err)
	}

	fmt.Println("微信支付客户端创建成功")

	// 设置自动验签
	err = client.AutoVerifySign()
	if err != nil {
		log.Fatalf("Failed to setup auto verify: %v", err)
	}
	fmt.Println("Auto verify setup successful")

	// 测试查询订单
	orderNo := "20250823225357925ba5" // 从日志中获取的订单号
	fmt.Printf("\n开始查询订单: %s\n", orderNo)

	ctx := context.Background()
	
	// 使用商户订单号查询
	fmt.Println("使用商户订单号查询...")
	wxRsp, err := client.V3TransactionQueryOrder(ctx, wechatv3.OutTradeNo, orderNo)
	if err != nil {
		fmt.Printf("查询失败: %v\n", err)
		
		// 尝试获取更详细的错误信息
		if wxRsp != nil {
			fmt.Printf("响应状态码: %d\n", wxRsp.Code)
			fmt.Printf("响应错误信息: %s\n", wxRsp.Error)
			fmt.Printf("响应体: %+v\n", wxRsp.Response)
		}
		
		return
	}

	fmt.Printf("查询成功!\n")
	fmt.Printf("响应状态码: %d\n", wxRsp.Code)
	if wxRsp.Response != nil {
		fmt.Printf("订单号: %s\n", wxRsp.Response.OutTradeNo)
		fmt.Printf("微信订单号: %s\n", wxRsp.Response.TransactionId)
		fmt.Printf("订单状态: %s\n", wxRsp.Response.TradeState)
		fmt.Printf("订单金额: %d 分\n", wxRsp.Response.Amount.Total)
		fmt.Printf("支付时间: %s\n", wxRsp.Response.SuccessTime)
	}
}
