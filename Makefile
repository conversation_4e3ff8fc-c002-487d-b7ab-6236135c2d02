.PHONY: help build run test clean docker-build docker-run docker-stop deps fmt lint

# 默认目标
help:
	@echo "可用的命令:"
	@echo "  build        - 构建应用"
	@echo "  run          - 运行应用"
	@echo "  test         - 运行测试"
	@echo "  clean        - 清理构建文件"
	@echo "  docker-build - 构建Docker镜像"
	@echo "  docker-run   - 使用Docker Compose启动服务"
	@echo "  docker-stop  - 停止Docker Compose服务"
	@echo "  deps         - 下载依赖"
	@echo "  fmt          - 格式化代码"
	@echo "  lint         - 代码检查"

# 构建应用
build:
	@echo "构建应用..."
	mkdir -p bin
	go build -o bin/pay-core cmd/server/main.go

# 运行应用
run:
	@echo "运行应用..."
	go run cmd/server/main.go

# 运行测试
test:
	@echo "运行测试..."
	go test -v ./...

# 清理构建文件
clean:
	@echo "清理构建文件..."
	rm -rf bin/
	go clean

# 构建Docker镜像
docker-build:
	@echo "构建Docker镜像..."
	docker build -t pay-core:latest .

# 使用Docker Compose启动服务
docker-run:
	@echo "启动Docker Compose服务..."
	docker-compose up -d

# 停止Docker Compose服务
docker-stop:
	@echo "停止Docker Compose服务..."
	docker-compose down

# 下载依赖
deps:
	@echo "下载依赖..."
	go mod download
	go mod tidy

# 格式化代码
fmt:
	@echo "格式化代码..."
	go fmt ./...

# 代码检查
lint:
	@echo "代码检查..."
	golangci-lint run

# 初始化开发环境
dev-setup:
	@echo "初始化开发环境..."
	go mod download
	docker-compose up -d mysql redis
	sleep 10
	@echo "等待数据库启动..."

# 运行API测试
api-test:
	@echo "运行API测试..."
	chmod +x scripts/test.sh
	./scripts/test.sh

# 生成API文档
docs:
	@echo "生成API文档..."
	swag init -g cmd/server/main.go -o docs/swagger

# 热重载开发
dev:
	@echo "启动热重载开发..."
	air

# 数据库迁移
migrate:
	@echo "执行数据库迁移..."
	mysql -h localhost -P 3306 -u root -p123456 < scripts/init.sql
