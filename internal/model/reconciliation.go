package model

import (
	"pay-core/pkg/types"
	"github.com/shopspring/decimal"
)

// ReconciliationBatch 对账批次模型
type ReconciliationBatch struct {
	ID           uint64    `json:"id" gorm:"primaryKey;autoIncrement"`
	BatchNo      string    `json:"batch_no" gorm:"type:varchar(64);uniqueIndex;not null;comment:对账批次号"`
	PayChannel   string    `json:"pay_channel" gorm:"type:varchar(32);not null;comment:支付渠道"`
	ReconcileDate types.Time `json:"reconcile_date" gorm:"type:date;not null;comment:对账日期"`
	Status       string    `json:"status" gorm:"type:varchar(32);not null;default:'PROCESSING';comment:对账状态"`
	StartedAt    *types.Time `json:"started_at" gorm:"comment:开始时间"`
	FinishedAt   *types.Time `json:"finished_at" gorm:"comment:完成时间"`
	CreatedAt    types.Time `json:"created_at"`
	UpdatedAt    types.Time `json:"updated_at"`
}

// TableName 指定表名
func (ReconciliationBatch) TableName() string {
	return "reconciliation_batches"
}

// ReconciliationDetail 对账明细模型
type ReconciliationDetail struct {
	ID              uint64          `json:"id" gorm:"primaryKey;autoIncrement"`
	BatchID         uint64          `json:"batch_id" gorm:"not null;index;comment:对账批次ID"`
	TransactionType string          `json:"transaction_type" gorm:"type:varchar(32);not null;default:'PAYMENT';comment:交易类型"`
	TransactionNo   string          `json:"transaction_no" gorm:"type:varchar(64);index;comment:交易单号"`
	OutTradeNo      string          `json:"out_trade_no" gorm:"type:varchar(64);comment:第三方订单号"`
	TradeNo         string          `json:"trade_no" gorm:"type:varchar(128);comment:第三方交易号"`
	PlatformAmount  decimal.Decimal `json:"platform_amount" gorm:"type:decimal(10,2);comment:平台金额"`
	PlatformStatus  string          `json:"platform_status" gorm:"type:varchar(32);comment:平台状态"`
	PlatformTime    *types.Time     `json:"platform_time" gorm:"comment:平台交易时间"`
	ChannelAmount   decimal.Decimal `json:"channel_amount" gorm:"type:decimal(10,2);comment:渠道金额"`
	ChannelStatus   string          `json:"channel_status" gorm:"type:varchar(32);comment:渠道状态"`
	ChannelTime     *types.Time     `json:"channel_time" gorm:"comment:渠道交易时间"`
	ReconcileResult string          `json:"reconcile_result" gorm:"type:varchar(32);not null;comment:对账结果"`
	DiffReason      string          `json:"diff_reason" gorm:"type:varchar(255);comment:差异原因"`
	CreatedAt       types.Time      `json:"created_at"`
}

// TableName 指定表名
func (ReconciliationDetail) TableName() string {
	return "reconciliation_details"
}

