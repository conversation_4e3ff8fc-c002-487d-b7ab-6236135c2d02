package model

import (
	"crypto/rand"
	"encoding/hex"

	"pay-core/pkg/types"
	"gorm.io/gorm"
)

// MerchantApp 接入应用模型
type MerchantApp struct {
	ID        uint64    `json:"id" gorm:"primaryKey;autoIncrement"`
	AppCode   string    `json:"app_code" gorm:"type:varchar(64);uniqueIndex;not null;comment:应用编码"`
	AppName   string    `json:"app_name" gorm:"type:varchar(128);not null;comment:应用名称"`
	AppSecret string    `json:"-" gorm:"type:varchar(256);not null;comment:应用密钥"`
	NotifyURL string    `json:"notify_url" gorm:"type:varchar(512);comment:支付结果回调地址"`
	Status    bool      `json:"status" gorm:"not null;default:true;comment:状态：1-启用，0-禁用"`
	CreatedAt types.Time `json:"created_at"`
	UpdatedAt types.Time `json:"updated_at"`
}

// TableName 指定表名
func (MerchantApp) TableName() string {
	return "merchant_apps"
}

// BeforeCreate GORM钩子，创建前生成AppCode和AppSecret
func (app *MerchantApp) BeforeCreate(tx *gorm.DB) error {
	if app.AppCode == "" {
		app.AppCode = generateAppCode()
	}
	if app.AppSecret == "" {
		app.AppSecret = generateAppSecret()
	}
	return nil
}

// IsActive 检查应用是否启用
func (app *MerchantApp) IsActive() bool {
	return app.Status
}

// CreateAppRequest 创建应用请求
type CreateAppRequest struct {
	AppName   string `json:"app_name" binding:"required,max=128"`
	NotifyURL string `json:"notify_url" binding:"omitempty,url"`
}

// UpdateAppRequest 更新应用请求
type UpdateAppRequest struct {
	AppName   string `json:"app_name" binding:"omitempty,max=128"`
	NotifyURL string `json:"notify_url" binding:"omitempty,url"`
	Status    *bool  `json:"status" binding:"omitempty"`
}

// AppListRequest 应用列表请求
type AppListRequest struct {
	Page     int    `form:"page" binding:"omitempty,min=1"`
	PageSize int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	Status   *bool  `form:"status" binding:"omitempty"`
	Keyword  string `form:"keyword" binding:"omitempty,max=50"`
}

// AppListResponse 应用列表响应
type AppListResponse struct {
	List     []*MerchantApp `json:"list"`
	Total    int64          `json:"total"`
	Page     int            `json:"page"`
	PageSize int            `json:"page_size"`
}

// generateAppCode 生成AppCode
func generateAppCode() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return "app_" + hex.EncodeToString(bytes)
}

// generateAppSecret 生成AppSecret
func generateAppSecret() string {
	bytes := make([]byte, 32)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}
