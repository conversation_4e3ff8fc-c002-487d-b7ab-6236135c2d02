package model

import "pay-core/pkg/types"

// NotifyLog 回调日志模型
type NotifyLog struct {
	ID           uint64    `json:"id" gorm:"primaryKey;autoIncrement"`
	OrderNo      string    `json:"order_no" gorm:"type:varchar(64);not null;index;comment:订单号"`
	AppID        *uint64   `json:"app_id" gorm:"index;comment:应用ID"`
	NotifyType   string    `json:"notify_type" gorm:"type:varchar(32);not null;comment:回调类型"`
	NotifySource string    `json:"notify_source" gorm:"type:varchar(32);not null;comment:回调来源"`
	NotifyURL    string    `json:"notify_url" gorm:"type:varchar(512);comment:回调地址"`
	RequestBody  string    `json:"request_body" gorm:"type:text;not null;comment:请求内容"`
	ResponseCode int       `json:"response_code" gorm:"comment:响应状态码"`
	ResponseBody string    `json:"response_body" gorm:"type:text;comment:响应内容"`
	Status       string    `json:"status" gorm:"type:varchar(32);not null;comment:状态"`
	RetryCount   int       `json:"retry_count" gorm:"default:0;comment:重试次数"`
	ErrorMsg     string    `json:"error_msg" gorm:"type:varchar(1024);comment:错误信息"`
	CreatedAt    types.Time `json:"created_at"`
}

// TableName 指定表名
func (NotifyLog) TableName() string {
	return "notify_logs"
}

