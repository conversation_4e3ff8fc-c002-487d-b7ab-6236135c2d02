package model

import (
	"database/sql/driver"
	"encoding/json"

	"pay-core/pkg/types"
	"github.com/shopspring/decimal"
)

// PaymentOrder 支付订单模型
type PaymentOrder struct {
	ID              uint64          `json:"id" gorm:"primaryKey;autoIncrement"`
	OrderNo         string          `json:"order_no" gorm:"type:varchar(64);uniqueIndex;not null;comment:支付系统订单号"`
	OutTradeNo      string          `json:"out_trade_no" gorm:"type:varchar(64);index;comment:第三方支付订单号"`
	AppID           uint64          `json:"app_id" gorm:"not null;comment:应用ID"`
	AppOrderNo      string          `json:"app_order_no" gorm:"type:varchar(128);not null;uniqueIndex:uk_app_order;comment:业务方订单号"`
	AppUserID       string          `json:"app_user_id" gorm:"type:varchar(128);comment:业务方用户ID"`
	PayChannel      string          `json:"pay_channel" gorm:"type:varchar(32);not null;comment:支付渠道"`
	TotalAmount     decimal.Decimal `json:"total_amount" gorm:"type:decimal(10,2);not null;comment:订单金额（元）"`
	Subject         string          `json:"subject" gorm:"type:varchar(256);not null;comment:商品标题"`
	Body            string          `json:"body" gorm:"type:varchar(512);comment:商品描述"`
	Status          string          `json:"status" gorm:"type:varchar(32);not null;default:'WAITING_PAY';comment:订单状态"`
	ExpireTime      types.Time      `json:"expire_time" gorm:"not null;comment:订单过期时间"`
	PaidAt          *types.Time     `json:"paid_at" gorm:"comment:支付完成时间"`
	PayInfo         JSON            `json:"pay_info" gorm:"type:json;comment:支付信息"`
	PayerInfo       JSON            `json:"payer_info" gorm:"type:json;comment:付款方信息"`
	AppNotifyStatus string          `json:"app_notify_status" gorm:"type:varchar(32);default:'PENDING';comment:业务回调状态"`
	Attach          string          `json:"attach" gorm:"type:varchar(1024);comment:附加数据"`
	CreatedAt       types.Time      `json:"created_at"`
	UpdatedAt       types.Time      `json:"updated_at"`

	// 关联
	App *MerchantApp `json:"app,omitempty" gorm:"foreignKey:AppID"`
}

// PaymentRecord 支付记录模型
type PaymentRecord struct {
	ID               uint64          `json:"id" gorm:"primaryKey;autoIncrement"`
	OrderNo          string          `json:"order_no" gorm:"type:varchar(64);not null;index;comment:支付系统订单号"`
	OutTradeNo       string          `json:"out_trade_no" gorm:"type:varchar(64);not null;uniqueIndex;comment:第三方支付订单号"`
	TradeNo          string          `json:"trade_no" gorm:"type:varchar(128);index;comment:第三方平台交易号"`
	PayChannel       string          `json:"pay_channel" gorm:"type:varchar(32);not null;comment:支付渠道"`
	TotalAmount      decimal.Decimal `json:"total_amount" gorm:"type:decimal(10,2);not null;comment:订单金额"`
	ReceiptAmount    decimal.Decimal `json:"receipt_amount" gorm:"type:decimal(10,2);comment:实收金额"`
	FeeAmount        decimal.Decimal `json:"fee_amount" gorm:"type:decimal(10,2);comment:手续费"`
	TradeTime        types.Time      `json:"trade_time" gorm:"not null;comment:交易时间"`
	ReconcileStatus  string          `json:"reconcile_status" gorm:"type:varchar(32);default:'PENDING';comment:对账状态"`
	SettlementStatus string          `json:"settlement_status" gorm:"type:varchar(32);default:'PENDING';comment:结算状态"`
	SettlementID     *uint64         `json:"settlement_id" gorm:"index;comment:结算记录ID"`
	CreatedAt        types.Time      `json:"created_at"`
	UpdatedAt        types.Time      `json:"updated_at"`
}

// TableName 指定表名
func (PaymentRecord) TableName() string {
	return "payment_records"
}

// TableName 指定表名
func (PaymentOrder) TableName() string {
	return "payment_orders"
}

// JSON 自定义JSON类型
type JSON map[string]interface{}

// Value 实现 driver.Valuer 接口
func (j JSON) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现 sql.Scanner 接口
func (j *JSON) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil // 或者返回一个错误
	}

	return json.Unmarshal(bytes, j)
}

// 订单状态常量
const (
	OrderStatusWaitingPay = "WAITING_PAY" // 待支付
	OrderStatusPaid       = "PAID"        // 已支付
	OrderStatusClosed     = "CLOSED"      // 已关闭
	OrderStatusRefunded   = "REFUNDED"    // 已退款
)

// 退款状态常量
const (
	RefundStatusProcessing = "PROCESSING" // 处理中
	RefundStatusSuccess    = "SUCCESS"    // 成功
	RefundStatusFailed     = "FAILED"     // 失败
)

// 支付渠道常量
const (
	PaymentChannelAlipay = "ALIPAY" // 支付宝
	PaymentChannelWechat = "WECHAT" // 微信支付
)

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	MerchantOrderNo string          `json:"merchant_order_no" binding:"omitempty,max=64"`
	Subject         string          `json:"subject" binding:"omitempty,max=256"`
	Body            string          `json:"body" binding:"omitempty,max=512"`
	Amount          decimal.Decimal `json:"amount" binding:"required,gt=0"`
	Currency        string          `json:"currency" binding:"omitempty,len=3"`
	PaymentMethod   string          `json:"payment_method" binding:"omitempty,oneof=native h5 app mini"`
	PaymentChannel  string          `json:"payment_channel" binding:"omitempty,oneof=alipay wechat qq"`
	AppUserID       string          `json:"app_user_id" binding:"omitempty,max=128"`           // 应用用户ID
	UserID          *uint64         `json:"user_id"`                                           // 兼容旧字段
	ExpireMinutes   int             `json:"expire_minutes" binding:"omitempty,min=1,max=1440"` // 过期时间（分钟）
	NotifyURL       string          `json:"notify_url" binding:"omitempty,url"`
	Attach          string          `json:"attach" binding:"omitempty,max=1024"`
}

// CreateOrderResponse 创建订单响应
type CreateOrderResponse struct {
	OrderNo     string          `json:"order_no"`
	TotalAmount decimal.Decimal `json:"total_amount"`
	PayInfo     JSON            `json:"pay_info"`
	ExpireTime  types.Time      `json:"expire_time"`
	PayChannel  string          `json:"pay_channel"`
}

// QueryOrderRequest 查询订单请求
type QueryOrderRequest struct {
	OrderNo    string `form:"order_no" binding:"omitempty"`
	AppOrderNo string `form:"app_order_no" binding:"omitempty"`
	OutTradeNo string `form:"out_trade_no" binding:"omitempty"`
}

// RefundOrderRequest 退款请求
type RefundOrderRequest struct {
	AppRefundNo  string          `json:"app_refund_no" binding:"required,max=128"`
	RefundAmount decimal.Decimal `json:"refund_amount" binding:"required,gt=0"`
	RefundReason string          `json:"refund_reason" binding:"omitempty,max=255"`
}

// RobotOrderResponse 机器人查询订单响应
type RobotOrderResponse struct {
	OrderNo     string          `json:"order_no"`
	TotalAmount decimal.Decimal `json:"total_amount"`
	Status      string          `json:"status"`
	StatusText  string          `json:"status_text"`
	IsPaid      bool            `json:"is_paid"`
	IsExpired   bool            `json:"is_expired"`
	PaidAt      *types.Time     `json:"paid_at"`
	ExpireTime  types.Time      `json:"expire_time"`
	PayChannel  string          `json:"pay_channel"`
	Subject     string          `json:"subject"`
}

// IsExpired 检查订单是否过期
func (o *PaymentOrder) IsExpired() bool {
	return types.Now().After(o.ExpireTime)
}

// IsPaid 检查订单是否已支付
func (o *PaymentOrder) IsPaid() bool {
	return o.Status == OrderStatusPaid
}

// CanRefund 检查订单是否可以退款
func (o *PaymentOrder) CanRefund() bool {
	return o.Status == OrderStatusPaid
}

// GetStatusText 获取状态文本
func (o *PaymentOrder) GetStatusText() string {
	switch o.Status {
	case OrderStatusWaitingPay:
		return "待支付"
	case OrderStatusPaid:
		return "支付成功"
	case OrderStatusClosed:
		return "已关闭"
	case OrderStatusRefunded:
		return "已退款"
	default:
		return "未知状态"
	}
}
