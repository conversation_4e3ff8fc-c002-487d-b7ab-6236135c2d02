package model

import (
	"pay-core/pkg/types"
	"github.com/shopspring/decimal"
)

// PaymentChannel 支付渠道配置模型
type PaymentChannel struct {
	ID          uint64    `json:"id" gorm:"primaryKey;autoIncrement"`
	ChannelCode string    `json:"channel_code" gorm:"type:varchar(32);uniqueIndex;not null;comment:渠道编码"`
	ChannelName string    `json:"channel_name" gorm:"type:varchar(64);not null;comment:渠道名称"`
	Provider    string    `json:"provider" gorm:"type:varchar(32);not null;comment:支付提供商"`
	IsActive    bool      `json:"is_active" gorm:"not null;default:true;comment:是否启用"`
	AppID       string    `json:"app_id" gorm:"type:varchar(128);comment:应用ID"`
	MchID       string    `json:"mch_id" gorm:"type:varchar(128);comment:商户号"`
	APIKey      string    `json:"api_key" gorm:"type:varchar(256);comment:API密钥"`
	PrivateKey  string    `json:"private_key" gorm:"type:text;comment:私钥"`
	CreatedAt   types.Time `json:"created_at"`
	UpdatedAt   types.Time `json:"updated_at"`
}

// TableName 指定表名
func (PaymentChannel) TableName() string {
	return "payment_channels"
}

// SettlementRecord 结算记录模型
type SettlementRecord struct {
	ID            uint64          `json:"id" gorm:"primaryKey;autoIncrement"`
	SettlementNo  string          `json:"settlement_no" gorm:"type:varchar(64);uniqueIndex;not null;comment:结算单号"`
	PayChannel    string          `json:"pay_channel" gorm:"type:varchar(32);not null;comment:支付渠道"`
	SettlementDate types.Time     `json:"settlement_date" gorm:"type:date;not null;comment:结算日期"`
	TotalAmount   decimal.Decimal `json:"total_amount" gorm:"type:decimal(15,2);not null;comment:结算总金额"`
	FeeAmount     decimal.Decimal `json:"fee_amount" gorm:"type:decimal(15,2);default:0.00;comment:手续费金额"`
	ActualAmount  decimal.Decimal `json:"actual_amount" gorm:"type:decimal(15,2);not null;comment:实际到账金额"`
	OrderCount    int             `json:"order_count" gorm:"not null;comment:订单笔数"`
	Status        string          `json:"status" gorm:"type:varchar(32);not null;default:'PENDING';comment:结算状态"`
	SettledAt     *types.Time     `json:"settled_at" gorm:"comment:结算时间"`
	CreatedAt     types.Time      `json:"created_at"`
	UpdatedAt     types.Time      `json:"updated_at"`
}

// TableName 指定表名
func (SettlementRecord) TableName() string {
	return "settlement_records"
}

