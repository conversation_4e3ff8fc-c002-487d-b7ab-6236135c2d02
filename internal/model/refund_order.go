package model

import (
	"pay-core/pkg/types"
	"github.com/shopspring/decimal"
)

// RefundOrder 退款订单模型
type RefundOrder struct {
	ID            uint64          `json:"id" gorm:"primaryKey;autoIncrement"`
	RefundNo      string          `json:"refund_no" gorm:"type:varchar(64);uniqueIndex;not null;comment:退款单号"`
	OrderNo       string          `json:"order_no" gorm:"type:varchar(64);not null;index;comment:原支付订单号"`
	AppID         uint64          `json:"app_id" gorm:"not null;comment:应用ID"`
	AppRefundNo   string          `json:"app_refund_no" gorm:"type:varchar(128);not null;uniqueIndex:uk_app_refund;comment:业务方退款单号"`
	RefundAmount  decimal.Decimal `json:"refund_amount" gorm:"type:decimal(10,2);not null;comment:退款金额"`
	RefundReason  string          `json:"refund_reason" gorm:"type:varchar(255);comment:退款原因"`
	Status        string          `json:"status" gorm:"type:varchar(32);not null;default:'PROCESSING';comment:退款状态"`
	OutRefundNo   string          `json:"out_refund_no" gorm:"type:varchar(64);comment:第三方退款单号"`
	SuccessTime   *types.Time     `json:"success_time" gorm:"comment:退款成功时间"`
	CreatedAt     types.Time      `json:"created_at"`
	UpdatedAt     types.Time      `json:"updated_at"`
}

// TableName 指定表名
func (RefundOrder) TableName() string {
	return "refund_orders"
}

// RefundRecord 退款记录模型
type RefundRecord struct {
	ID                 uint64          `json:"id" gorm:"primaryKey;autoIncrement"`
	RefundNo           string          `json:"refund_no" gorm:"type:varchar(64);not null;index;comment:退款单号"`
	OutRefundNo        string          `json:"out_refund_no" gorm:"type:varchar(64);not null;uniqueIndex;comment:第三方退款单号"`
	RefundTradeNo      string          `json:"refund_trade_no" gorm:"type:varchar(128);comment:第三方退款交易号"`
	PayChannel         string          `json:"pay_channel" gorm:"type:varchar(32);not null;comment:支付渠道"`
	OrderNo            string          `json:"order_no" gorm:"type:varchar(64);not null;index;comment:原支付订单号"`
	OutTradeNo         string          `json:"out_trade_no" gorm:"type:varchar(64);index;comment:原第三方支付订单号"`
	RefundAmount       decimal.Decimal `json:"refund_amount" gorm:"type:decimal(10,2);not null;comment:申请退款金额"`
	ActualRefundAmount decimal.Decimal `json:"actual_refund_amount" gorm:"type:decimal(10,2);comment:实际退款金额"`
	RefundFee          decimal.Decimal `json:"refund_fee" gorm:"type:decimal(10,2);comment:退款手续费"`
	RefundTime         types.Time      `json:"refund_time" gorm:"not null;comment:退款时间"`
	ReconcileStatus    string          `json:"reconcile_status" gorm:"type:varchar(32);default:'PENDING';comment:对账状态"`
	SettlementStatus   string          `json:"settlement_status" gorm:"type:varchar(32);default:'PENDING';comment:结算状态"`
	SettlementID       *uint64         `json:"settlement_id" gorm:"index;comment:结算记录ID"`
	CreatedAt          types.Time      `json:"created_at"`
	UpdatedAt          types.Time      `json:"updated_at"`
}

// TableName 指定表名
func (RefundRecord) TableName() string {
	return "refund_records"
}

