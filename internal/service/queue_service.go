package service

import (
	"context"
	"fmt"
	"time"

	"pay-core/pkg/logger"
	"pay-core/pkg/queue"

	"github.com/redis/go-redis/v9"
)

// QueueService 队列处理服务接口
type QueueService interface {
	Start() error
	Stop() error
	AddOrderExpireTask(orderNo string, expireTime time.Time) error
	AddOrderPollTask(orderNo string, pollTime time.Time, pollCount int) error
	RemoveOrderExpireTask(orderNo string) error
	RemoveOrderPollTask(orderNo string, pollCount int) error
}

// queueService 队列处理服务实现
type queueService struct {
	delayQueue     *queue.DelayQueue
	paymentService PaymentService
	redis          *redis.Client
	stopChan       chan struct{}
	running        bool
}

// NewQueueService 创建队列处理服务
func NewQueueService(redis *redis.Client, paymentService PaymentService) QueueService {
	delayQueue := queue.NewDelayQueue(redis, "pay_core")

	return &queueService{
		delayQueue:     delayQueue,
		paymentService: paymentService,
		redis:          redis,
		stopChan:       make(chan struct{}),
		running:        false,
	}
}

// Start 启动队列处理服务
func (qs *queueService) Start() error {
	if qs.running {
		return fmt.Errorf("queue service is already running")
	}

	qs.running = true
	logger.Info("Starting queue service")

	// 启动订单过期任务处理器
	go qs.processOrderExpireTasks()

	// 启动订单轮询任务处理器
	go qs.processOrderPollTasks()

	logger.Info("Queue service started successfully")
	return nil
}

// Stop 停止队列处理服务
func (qs *queueService) Stop() error {
	if !qs.running {
		return nil
	}

	logger.Info("Stopping queue service")
	qs.running = false
	close(qs.stopChan)

	logger.Info("Queue service stopped")
	return nil
}

// AddOrderExpireTask 添加订单过期任务
func (qs *queueService) AddOrderExpireTask(orderNo string, expireTime time.Time) error {
	task := queue.CreateOrderExpireTask(orderNo, expireTime)

	err := qs.delayQueue.AddTask(context.Background(), task)
	if err != nil {
		logger.WithError(err).WithFields(map[string]interface{}{
			"order_no":    orderNo,
			"expire_time": expireTime,
		}).Error("Failed to add order expire task")
		return err
	}

	logger.WithFields(map[string]interface{}{
		"order_no":    orderNo,
		"expire_time": expireTime,
		"task_id":     task.ID,
	}).Debug("Added order expire task to queue")

	return nil
}

// AddOrderPollTask 添加订单轮询任务
func (qs *queueService) AddOrderPollTask(orderNo string, pollTime time.Time, pollCount int) error {
	task := queue.CreateOrderPollTask(orderNo, pollTime, pollCount)

	err := qs.delayQueue.AddTask(context.Background(), task)
	if err != nil {
		logger.WithError(err).WithFields(map[string]interface{}{
			"order_no":   orderNo,
			"poll_time":  pollTime,
			"poll_count": pollCount,
		}).Error("Failed to add order poll task")
		return err
	}

	logger.WithFields(map[string]interface{}{
		"order_no":   orderNo,
		"poll_time":  pollTime,
		"poll_count": pollCount,
		"task_id":    task.ID,
	}).Debug("Added order poll task to queue")

	return nil
}

// RemoveOrderExpireTask 移除订单过期任务
func (qs *queueService) RemoveOrderExpireTask(orderNo string) error {
	taskID := fmt.Sprintf("expire_%s", orderNo)
	return qs.delayQueue.RemoveTask(context.Background(), queue.TaskTypeOrderExpire, taskID)
}

// RemoveOrderPollTask 移除订单轮询任务
func (qs *queueService) RemoveOrderPollTask(orderNo string, pollCount int) error {
	taskID := fmt.Sprintf("poll_%s_%d", orderNo, pollCount)
	return qs.delayQueue.RemoveTask(context.Background(), queue.TaskTypeOrderPoll, taskID)
}

// processOrderExpireTasks 处理订单过期任务
func (qs *queueService) processOrderExpireTasks() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-qs.stopChan:
			logger.Info("Order expire task processor stopped")
			return
		case <-ticker.C:
			qs.handleOrderExpireTasks()
		}
	}
}

// processOrderPollTasks 处理订单轮询任务
func (qs *queueService) processOrderPollTasks() {
	ticker := time.NewTicker(10 * time.Second) // 每10秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-qs.stopChan:
			logger.Info("Order poll task processor stopped")
			return
		case <-ticker.C:
			qs.handleOrderPollTasks()
		}
	}
}

// handleOrderExpireTasks 处理订单过期任务
func (qs *queueService) handleOrderExpireTasks() {
	ctx := context.Background()

	tasks, err := qs.delayQueue.GetReadyTasks(ctx, queue.TaskTypeOrderExpire, 50)
	if err != nil {
		logger.WithError(err).Error("Failed to get ready order expire tasks")
		return
	}

	if len(tasks) == 0 {
		return
	}

	logger.WithField("task_count", len(tasks)).Info("Processing order expire tasks")

	for _, task := range tasks {
		orderNo, ok := task.Data["order_no"].(string)
		if !ok {
			logger.WithField("task_id", task.ID).Error("Invalid order_no in expire task")
			continue
		}

		if err := qs.processOrderExpireTask(ctx, orderNo); err != nil {
			logger.WithError(err).WithFields(map[string]interface{}{
				"task_id":  task.ID,
				"order_no": orderNo,
			}).Error("Failed to process order expire task")

			// 重试任务
			if retryErr := qs.delayQueue.RetryTask(ctx, task, 5*time.Minute); retryErr != nil {
				logger.WithError(retryErr).Error("Failed to retry order expire task")
			}
		} else {
			logger.WithFields(map[string]interface{}{
				"task_id":  task.ID,
				"order_no": orderNo,
			}).Info("Successfully processed order expire task")
		}
	}
}

// handleOrderPollTasks 处理订单轮询任务
func (qs *queueService) handleOrderPollTasks() {
	ctx := context.Background()

	tasks, err := qs.delayQueue.GetReadyTasks(ctx, queue.TaskTypeOrderPoll, 30)
	if err != nil {
		logger.WithError(err).Error("Failed to get ready order poll tasks")
		return
	}

	if len(tasks) == 0 {
		return
	}

	logger.WithField("task_count", len(tasks)).Info("Processing order poll tasks")

	for _, task := range tasks {
		orderNo, ok := task.Data["order_no"].(string)
		if !ok {
			logger.WithField("task_id", task.ID).Error("Invalid order_no in poll task")
			continue
		}

		pollCount, ok := task.Data["poll_count"].(float64) // JSON数字默认为float64
		if !ok {
			logger.WithField("task_id", task.ID).Error("Invalid poll_count in poll task")
			continue
		}

		if err := qs.processOrderPollTask(ctx, orderNo, int(pollCount)); err != nil {
			logger.WithError(err).WithFields(map[string]interface{}{
				"task_id":    task.ID,
				"order_no":   orderNo,
				"poll_count": int(pollCount),
			}).Error("Failed to process order poll task")

			// 重试任务
			if retryErr := qs.delayQueue.RetryTask(ctx, task, 2*time.Minute); retryErr != nil {
				logger.WithError(retryErr).Error("Failed to retry order poll task")
			}
		} else {
			logger.WithFields(map[string]interface{}{
				"task_id":    task.ID,
				"order_no":   orderNo,
				"poll_count": int(pollCount),
			}).Info("Successfully processed order poll task")
		}
	}
}

// processOrderExpireTask 处理单个订单过期任务
func (qs *queueService) processOrderExpireTask(ctx context.Context, orderNo string) error {
	// 调用PaymentService的关闭过期订单方法
	return qs.paymentService.CloseExpiredOrder(ctx, orderNo)
}

// processOrderPollTask 处理单个订单轮询任务
func (qs *queueService) processOrderPollTask(ctx context.Context, orderNo string, pollCount int) error {
	// 调用PaymentService的轮询订单状态方法
	return qs.paymentService.PollOrderStatus(ctx, orderNo, pollCount)
}
