package service

import (
	"context"
	"net/http"
	"testing"

	"pay-core/internal/config"
	"pay-core/internal/model"
	"pay-core/pkg/payment"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"
)

// MockPaymentOrderRepository 模拟支付订单仓储
type MockPaymentOrderRepository struct {
	mock.Mock
}

func (m *MockPaymentOrderRepository) Create(ctx context.Context, order *model.PaymentOrder) error {
	args := m.Called(ctx, order)
	return args.Error(0)
}

func (m *MockPaymentOrderRepository) GetByOrderNo(ctx context.Context, orderNo string) (*model.PaymentOrder, error) {
	args := m.Called(ctx, orderNo)
	return args.Get(0).(*model.PaymentOrder), args.Error(1)
}

func (m *MockPaymentOrderRepository) GetByAppOrderNo(ctx context.Context, appID uint64, appOrderNo string) (*model.PaymentOrder, error) {
	args := m.Called(ctx, appID, appOrderNo)
	return args.Get(0).(*model.PaymentOrder), args.Error(1)
}

func (m *MockPaymentOrderRepository) GetByOutTradeNo(ctx context.Context, outTradeNo string) (*model.PaymentOrder, error) {
	args := m.Called(ctx, outTradeNo)
	return args.Get(0).(*model.PaymentOrder), args.Error(1)
}

func (m *MockPaymentOrderRepository) Update(ctx context.Context, order *model.PaymentOrder) error {
	args := m.Called(ctx, order)
	return args.Error(0)
}

func (m *MockPaymentOrderRepository) Delete(ctx context.Context, id uint64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockPaymentOrderRepository) GetExpiredOrders(ctx context.Context, limit int, scanDays int) ([]*model.PaymentOrder, error) {
	args := m.Called(ctx, limit, scanDays)
	return args.Get(0).([]*model.PaymentOrder), args.Error(1)
}

// MockPaymentRecordRepository 模拟支付记录仓储
type MockPaymentRecordRepository struct {
	mock.Mock
}

func (m *MockPaymentRecordRepository) Create(ctx context.Context, record *model.PaymentRecord) error {
	args := m.Called(ctx, record)
	return args.Error(0)
}

func (m *MockPaymentRecordRepository) GetByOrderNo(ctx context.Context, orderNo string) (*model.PaymentRecord, error) {
	args := m.Called(ctx, orderNo)
	return args.Get(0).(*model.PaymentRecord), args.Error(1)
}

func (m *MockPaymentRecordRepository) GetByTradeNo(ctx context.Context, tradeNo string) (*model.PaymentRecord, error) {
	args := m.Called(ctx, tradeNo)
	return args.Get(0).(*model.PaymentRecord), args.Error(1)
}

func (m *MockPaymentRecordRepository) Update(ctx context.Context, record *model.PaymentRecord) error {
	args := m.Called(ctx, record)
	return args.Error(0)
}

// MockRefundOrderRepository 模拟退款订单仓储
type MockRefundOrderRepository struct {
	mock.Mock
}

func (m *MockRefundOrderRepository) Create(ctx context.Context, order *model.RefundOrder) error {
	args := m.Called(ctx, order)
	return args.Error(0)
}

func (m *MockRefundOrderRepository) GetByRefundNo(ctx context.Context, refundNo string) (*model.RefundOrder, error) {
	args := m.Called(ctx, refundNo)
	return args.Get(0).(*model.RefundOrder), args.Error(1)
}

func (m *MockRefundOrderRepository) GetByAppRefundNo(ctx context.Context, appID uint64, appRefundNo string) (*model.RefundOrder, error) {
	args := m.Called(ctx, appID, appRefundNo)
	return args.Get(0).(*model.RefundOrder), args.Error(1)
}

func (m *MockRefundOrderRepository) Update(ctx context.Context, order *model.RefundOrder) error {
	args := m.Called(ctx, order)
	return args.Error(0)
}

// MockAppRepository 模拟应用仓储
type MockAppRepository struct {
	mock.Mock
}

func (m *MockAppRepository) Create(ctx context.Context, app *model.MerchantApp) error {
	args := m.Called(ctx, app)
	return args.Error(0)
}

func (m *MockAppRepository) GetByID(ctx context.Context, id uint64) (*model.MerchantApp, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.MerchantApp), args.Error(1)
}

func (m *MockAppRepository) GetByAppCode(ctx context.Context, appCode string) (*model.MerchantApp, error) {
	args := m.Called(ctx, appCode)
	return args.Get(0).(*model.MerchantApp), args.Error(1)
}

func (m *MockAppRepository) Update(ctx context.Context, app *model.MerchantApp) error {
	args := m.Called(ctx, app)
	return args.Error(0)
}

func (m *MockAppRepository) Delete(ctx context.Context, id uint64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockAppRepository) List(ctx context.Context, req *model.AppListRequest) ([]*model.MerchantApp, int64, error) {
	args := m.Called(ctx, req)
	return args.Get(0).([]*model.MerchantApp), args.Get(1).(int64), args.Error(2)
}

// MockPaymentClient 模拟支付客户端
type MockPaymentClient struct {
	mock.Mock
}

func (m *MockPaymentClient) CreateNativeOrder(req *payment.NativeOrderRequest) (*payment.NativeOrderResponse, error) {
	args := m.Called(req)
	return args.Get(0).(*payment.NativeOrderResponse), args.Error(1)
}

func (m *MockPaymentClient) QueryOrder(req *payment.QueryOrderRequest) (*payment.QueryOrderResponse, error) {
	args := m.Called(req)
	return args.Get(0).(*payment.QueryOrderResponse), args.Error(1)
}

func (m *MockPaymentClient) ParseNotify(ctx context.Context, channel string, req *http.Request) (*payment.NotifyData, error) {
	args := m.Called(ctx, channel, req)
	return args.Get(0).(*payment.NotifyData), args.Error(1)
}

// TestPaymentService_CreateOrder 测试创建订单
func TestPaymentService_CreateOrder(t *testing.T) {
	// 准备测试数据
	mockOrderRepo := &MockPaymentOrderRepository{}
	mockRecordRepo := &MockPaymentRecordRepository{}

	// 创建真实的支付客户端用于测试
	paymentConfig := config.PaymentConfig{
		Wechat: config.WechatConfig{
			AppID:  "test_app_id",
			MchID:  "test_mch_id",
			APIKey: "test_api_key",
			IsProd: false,
		},
	}
	paymentClient, _ := payment.NewClient(paymentConfig)

	// 创建模拟的其他依赖
	mockRefundRepo := &MockRefundOrderRepository{}
	mockAppRepo := &MockAppRepository{}

	service := &paymentService{
		paymentOrderRepo:  mockOrderRepo,
		refundOrderRepo:   mockRefundRepo,
		paymentRecordRepo: mockRecordRepo,
		appRepo:           mockAppRepo,
		paymentClient:     paymentClient,
		redis:             nil, // 测试中不需要redis
	}

	ctx := context.Background()
	appID := uint64(1)
	req := &model.CreateOrderRequest{
		MerchantOrderNo: "test_order_001",
		Subject:         "Test Product",
		Body:            "Test Product Description",
		Amount:          decimal.NewFromFloat(100.00),
		PaymentChannel:  "wechat",
		ExpireMinutes:   30,
		NotifyURL:       "http://example.com/notify",
	}

	// 设置模拟期望 - 注意：这个测试需要真实的支付配置才能完全工作
	// 在实际环境中应该使用沙箱环境

	// 模拟应用存在且活跃
	testApp := &model.MerchantApp{
		ID:      1,
		AppCode: "test_app",
		AppName: "Test App",
		Status:  true,
	}
	mockAppRepo.On("GetByID", ctx, uint64(1)).Return(testApp, nil)

	// 模拟订单不存在（用于检查重复）
	mockOrderRepo.On("GetByAppOrderNo", ctx, uint64(1), "test_order_001").Return((*model.PaymentOrder)(nil), gorm.ErrRecordNotFound)

	mockOrderRepo.On("Create", ctx, mock.AnythingOfType("*model.PaymentOrder")).Return(nil)
	mockOrderRepo.On("Update", ctx, mock.AnythingOfType("*model.PaymentOrder")).Return(nil)
	mockOrderRepo.On("Delete", ctx, mock.AnythingOfType("uint64")).Return(nil) // 支付失败时会删除订单

	// 执行测试
	resp, err := service.CreateOrder(ctx, appID, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.NotEmpty(t, resp.OrderNo)
	assert.Equal(t, req.Amount, resp.TotalAmount)
	assert.Equal(t, req.PaymentChannel, resp.PayChannel)
	assert.NotNil(t, resp.PayInfo)

	// 验证模拟调用
	mockOrderRepo.AssertExpectations(t)

	// 注意：这个测试需要真实的支付配置才能完全工作
	// 在实际环境中应该使用沙箱环境进行测试
	t.Log("Payment service create order test completed - requires real payment credentials for full functionality")
}
