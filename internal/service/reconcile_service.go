package service

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"pay-core/internal/model"
	"pay-core/internal/repository"
	"pay-core/pkg/logger"
	"pay-core/pkg/payment"
	"pay-core/pkg/types"
)

// ReconciliationService 对账服务接口
type ReconciliationService interface {
	StartReconciliation(ctx context.Context, date time.Time, channel string) (*model.ReconciliationBatch, error)
	GetBatch(ctx context.Context, batchNo string) (*model.ReconciliationBatch, error)
	GetDetails(ctx context.Context, batchID uint64) ([]*model.ReconciliationDetail, error)
}

// reconciliationService 对账服务实现
type reconciliationService struct {
	reconciliationRepo repository.ReconciliationRepository
	paymentOrderRepo   repository.PaymentOrderRepository
	paymentClient      *payment.Client
}

// NewReconciliationService 创建对账服务
func NewReconciliationService(reconciliationRepo repository.ReconciliationRepository, paymentOrderRepo repository.PaymentOrderRepository, paymentClient *payment.Client) ReconciliationService {
	return &reconciliationService{
		reconciliationRepo: reconciliationRepo,
		paymentOrderRepo:   paymentOrderRepo,
		paymentClient:      paymentClient,
	}
}

// StartReconciliation 开始对账
func (s *reconciliationService) StartReconciliation(ctx context.Context, date time.Time, channel string) (*model.ReconciliationBatch, error) {
	batchNo := generateBatchNo(date, channel)
	now := types.Now()

	batch := &model.ReconciliationBatch{
		BatchNo:       batchNo,
		ReconcileDate: types.NewTime(date),
		PayChannel:    channel,
		Status:        "PROCESSING",
		StartedAt:     &now,
	}

	if err := s.reconciliationRepo.CreateBatch(ctx, batch); err != nil {
		return nil, fmt.Errorf("failed to create reconcile batch: %w", err)
	}

	go s.performReconciliation(context.Background(), batch)

	return batch, nil
}

// GetBatch 获取对账批次详情
func (s *reconciliationService) GetBatch(ctx context.Context, batchNo string) (*model.ReconciliationBatch, error) {
	return s.reconciliationRepo.GetBatchByBatchNo(ctx, batchNo)
}

// GetDetails 获取对账明细列表
func (s *reconciliationService) GetDetails(ctx context.Context, batchID uint64) ([]*model.ReconciliationDetail, error) {
	return s.reconciliationRepo.ListDetailsByBatchID(ctx, batchID)
}

// performReconciliation 执行对账
func (s *reconciliationService) performReconciliation(ctx context.Context, batch *model.ReconciliationBatch) {
	logger.WithField("batch_no", batch.BatchNo).Info("Starting reconciliation")

	// ... (省略对账核心逻辑)

	// 模拟对账完成
	finishedAt := types.Now()
	batch.FinishedAt = &finishedAt
	batch.Status = "SUCCESS"

	if err := s.reconciliationRepo.UpdateBatch(ctx, batch); err != nil {
		logger.WithError(err).Error("Failed to update reconcile batch")
	}

	logger.WithField("batch_no", batch.BatchNo).Info("Reconciliation completed")
}

// generateBatchNo 生成批次号
func generateBatchNo(date time.Time, channel string) string {
	dateStr := date.Format("20060102")

	// 生成4位随机数
	bytes := make([]byte, 2)
	rand.Read(bytes)
	randomStr := hex.EncodeToString(bytes)

	return fmt.Sprintf("%s_%s_%s", channel, dateStr, randomStr)
}
