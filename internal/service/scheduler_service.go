package service

import (
	"context"
	"fmt"
	"time"

	"pay-core/internal/config"
	"pay-core/pkg/logger"
	"pay-core/pkg/types"

	"github.com/redis/go-redis/v9"
	"github.com/robfig/cron/v3"
)

// SchedulerService 定时任务服务接口
type SchedulerService interface {
	Start() error
	Stop() error
}

// schedulerService 定时任务服务实现
type schedulerService struct {
	cron             *cron.Cron
	paymentService   PaymentService
	reconcileService ReconciliationService
	redis            *redis.Client
	config           config.SchedulerConfig
}

// NewSchedulerService 创建定时任务服务
func NewSchedulerService(paymentService PaymentService, reconcileService ReconciliationService, redis *redis.Client, cfg config.SchedulerConfig) SchedulerService {
	return &schedulerService{
		cron:             cron.New(cron.WithSeconds()),
		paymentService:   paymentService,
		reconcileService: reconcileService,
		redis:            redis,
		config:           cfg,
	}
}

// Start 启动定时任务
func (s *schedulerService) Start() error {
	// 检查是否启用关闭过期订单任务
	if s.config.CloseExpiredOrders.Enabled {
		// 使用配置中的cron表达式
		_, err := s.cron.AddFunc(s.config.CloseExpiredOrders.Cron, s.closeExpiredOrders)
		if err != nil {
			logger.WithError(err).Error("Failed to add close expired orders job")
			return err
		}
		logger.WithField("cron", s.config.CloseExpiredOrders.Cron).Info("Added close expired orders job")
	}

	// 检查是否启用日对账任务
	if s.config.DailyReconcile.Enabled {
		// 使用配置中的cron表达式
		_, err := s.cron.AddFunc(s.config.DailyReconcile.Cron, s.dailyReconcile)
		if err != nil {
			logger.WithError(err).Error("Failed to add daily reconcile job")
			return err
		}
		logger.WithField("cron", s.config.DailyReconcile.Cron).Info("Added daily reconcile job")
	}

	// 启动定时任务调度器
	s.cron.Start()
	logger.Info("Scheduler service started")

	return nil
}

// Stop 停止定时任务
func (s *schedulerService) Stop() error {
	ctx := s.cron.Stop()
	select {
	case <-ctx.Done():
		logger.Info("Scheduler service stopped gracefully")
	case <-time.After(30 * time.Second):
		logger.Warn("Scheduler service stop timeout")
	}
	return nil
}

// closeExpiredOrders 关闭过期订单的定时任务
func (s *schedulerService) closeExpiredOrders() {
	cfg := s.config.CloseExpiredOrders

	// 尝试获取分布式锁
	lockKey := cfg.LockKey
	lockValue := fmt.Sprintf("%d", types.Now().UnixNano()) // 使用时间戳作为锁值
	lockTTL := time.Duration(cfg.LockTTL) * time.Second

	// 获取锁
	acquired, err := s.redis.SetNX(context.Background(), lockKey, lockValue, lockTTL).Result()
	if err != nil {
		logger.WithError(err).Error("Failed to acquire distributed lock")
		return
	}

	if !acquired {
		logger.Info("Another instance is processing expired orders, skipping")
		return
	}

	// 确保释放锁
	defer func() {
		// 使用Lua脚本安全释放锁（只释放自己的锁）
		luaScript := `
			if redis.call("get", KEYS[1]) == ARGV[1] then
				return redis.call("del", KEYS[1])
			else
				return 0
			end
		`
		s.redis.Eval(context.Background(), luaScript, []string{lockKey}, lockValue)
	}()

	logger.WithField("lock_key", lockKey).Info("Acquired distributed lock, starting to close expired orders")

	// 设置超时时间
	timeout := time.Duration(cfg.TimeoutMinutes) * time.Minute
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	err = s.paymentService.CloseExpiredOrders(ctx)
	if err != nil {
		logger.WithError(err).Error("Failed to close expired orders")
	} else {
		logger.Info("Successfully processed expired orders")
	}
}

// dailyReconcile 日对账定时任务
func (s *schedulerService) dailyReconcile() {
	cfg := s.config.DailyReconcile

	// 尝试获取分布式锁
	lockKey := cfg.LockKey
	lockValue := fmt.Sprintf("%d", types.Now().UnixNano())
	lockTTL := time.Duration(cfg.LockTTL) * time.Second

	// 获取锁
	acquired, err := s.redis.SetNX(context.Background(), lockKey, lockValue, lockTTL).Result()
	if err != nil {
		logger.WithError(err).Error("Failed to acquire distributed lock for reconcile")
		return
	}

	if !acquired {
		logger.Info("Another instance is processing daily reconcile, skipping")
		return
	}

	// 确保释放锁
	defer func() {
		// 使用Lua脚本安全释放锁
		luaScript := `
			if redis.call("get", KEYS[1]) == ARGV[1] then
				return redis.call("del", KEYS[1])
			else
				return 0
			end
		`
		s.redis.Eval(context.Background(), luaScript, []string{lockKey}, lockValue)
	}()

	logger.WithField("lock_key", lockKey).Info("Acquired distributed lock, starting daily reconcile")

	// 计算对账日期（前N天）
	reconcileDate := types.Now().Time.AddDate(0, 0, -cfg.DelayDays)

	// 设置超时时间
	timeout := time.Duration(cfg.TimeoutMinutes) * time.Minute
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 对每个渠道执行对账
	for _, channel := range cfg.Channels {
		logger.WithFields(map[string]interface{}{
			"date":    reconcileDate.Format("2006-01-02"),
			"channel": channel,
		}).Info("Starting reconcile for channel")

		_, err := s.reconcileService.StartReconciliation(ctx, reconcileDate, channel)
		if err != nil {
			logger.WithError(err).WithFields(map[string]interface{}{
				"date":    reconcileDate.Format("2006-01-02"),
				"channel": channel,
			}).Error("Failed to start reconcile for channel")
		} else {
			logger.WithFields(map[string]interface{}{
				"date":    reconcileDate.Format("2006-01-02"),
				"channel": channel,
			}).Info("Successfully started reconcile for channel")
		}
	}

	logger.Info("Daily reconcile task completed")
}
