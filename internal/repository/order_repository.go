package repository

import (
	"context"

	"pay-core/internal/model"
	"pay-core/pkg/types"

	"gorm.io/gorm"
)

// PaymentOrderRepository 支付订单仓储接口
type PaymentOrderRepository interface {
	Create(ctx context.Context, order *model.PaymentOrder) error
	GetByOrderNo(ctx context.Context, orderNo string) (*model.PaymentOrder, error)
	GetByAppOrderNo(ctx context.Context, appID uint64, appOrderNo string) (*model.PaymentOrder, error)
	GetByOutTradeNo(ctx context.Context, outTradeNo string) (*model.PaymentOrder, error)
	Update(ctx context.Context, order *model.PaymentOrder) error
	Delete(ctx context.Context, id uint64) error
	GetExpiredOrders(ctx context.Context, limit int, scanDays int) ([]*model.PaymentOrder, error)
}

// paymentOrderRepository 支付订单仓储实现
type paymentOrderRepository struct {
	db *gorm.DB
}

// NewPaymentOrderRepository 创建支付订单仓储
func NewPaymentOrderRepository(db *gorm.DB) PaymentOrderRepository {
	return &paymentOrderRepository{db: db}
}

// Create 创建订单
func (r *paymentOrderRepository) Create(ctx context.Context, order *model.PaymentOrder) error {
	return r.db.WithContext(ctx).Create(order).Error
}

// GetByOrderNo 根据订单号获取订单
func (r *paymentOrderRepository) GetByOrderNo(ctx context.Context, orderNo string) (*model.PaymentOrder, error) {
	var order model.PaymentOrder
	err := r.db.WithContext(ctx).Where("order_no = ?", orderNo).First(&order).Error
	return &order, err
}

// GetByAppOrderNo 根据业务方订单号获取订单
func (r *paymentOrderRepository) GetByAppOrderNo(ctx context.Context, appID uint64, appOrderNo string) (*model.PaymentOrder, error) {
	var order model.PaymentOrder
	err := r.db.WithContext(ctx).Where("app_id = ? AND app_order_no = ?", appID, appOrderNo).First(&order).Error
	return &order, err
}

// GetByOutTradeNo 根据第三方交易号获取订单
func (r *paymentOrderRepository) GetByOutTradeNo(ctx context.Context, outTradeNo string) (*model.PaymentOrder, error) {
	var order model.PaymentOrder
	err := r.db.WithContext(ctx).Where("out_trade_no = ?", outTradeNo).First(&order).Error
	return &order, err
}

// Update 更新订单
func (r *paymentOrderRepository) Update(ctx context.Context, order *model.PaymentOrder) error {
	return r.db.WithContext(ctx).Save(order).Error
}

// Delete 删除订单
func (r *paymentOrderRepository) Delete(ctx context.Context, id uint64) error {
	return r.db.WithContext(ctx).Delete(&model.PaymentOrder{}, id).Error
}

// GetExpiredOrders 获取过期订单
func (r *paymentOrderRepository) GetExpiredOrders(ctx context.Context, limit int, scanDays int) ([]*model.PaymentOrder, error) {
	var orders []*model.PaymentOrder
	scanStartTime := types.Now().Time.AddDate(0, 0, -scanDays)
	now := types.Now()

	err := r.db.WithContext(ctx).
		Where("status = ? AND expire_time < ? AND created_at >= ?", model.OrderStatusWaitingPay, now, scanStartTime).
		Order("expire_time ASC").
		Limit(limit).
		Find(&orders).Error

	return orders, err
}

// --- RefundOrderRepository ---

// RefundOrderRepository 退款订单仓储接口
type RefundOrderRepository interface {
	Create(ctx context.Context, refundOrder *model.RefundOrder) error
	GetByRefundNo(ctx context.Context, refundNo string) (*model.RefundOrder, error)
	GetByAppRefundNo(ctx context.Context, appID uint64, appRefundNo string) (*model.RefundOrder, error)
	Update(ctx context.Context, refundOrder *model.RefundOrder) error
}

// refundOrderRepository 退款订单仓储实现
type refundOrderRepository struct {
	db *gorm.DB
}

// NewRefundOrderRepository 创建退款订单仓储
func NewRefundOrderRepository(db *gorm.DB) RefundOrderRepository {
	return &refundOrderRepository{db: db}
}

// Create 创建退款订单
func (r *refundOrderRepository) Create(ctx context.Context, refundOrder *model.RefundOrder) error {
	return r.db.WithContext(ctx).Create(refundOrder).Error
}

// GetByRefundNo 根据退款单号获取退款订单
func (r *refundOrderRepository) GetByRefundNo(ctx context.Context, refundNo string) (*model.RefundOrder, error) {
	var refundOrder model.RefundOrder
	err := r.db.WithContext(ctx).Where("refund_no = ?", refundNo).First(&refundOrder).Error
	return &refundOrder, err
}

// GetByAppRefundNo 根据业务方退款单号获取退款订单
func (r *refundOrderRepository) GetByAppRefundNo(ctx context.Context, appID uint64, appRefundNo string) (*model.RefundOrder, error) {
	var refundOrder model.RefundOrder
	err := r.db.WithContext(ctx).Where("app_id = ? AND app_refund_no = ?", appID, appRefundNo).First(&refundOrder).Error
	return &refundOrder, err
}

// Update 更新退款订单
func (r *refundOrderRepository) Update(ctx context.Context, refundOrder *model.RefundOrder) error {
	return r.db.WithContext(ctx).Save(refundOrder).Error
}

// --- PaymentRecordRepository ---

// PaymentRecordRepository 支付记录仓储接口
type PaymentRecordRepository interface {
	Create(ctx context.Context, record *model.PaymentRecord) error
}

// paymentRecordRepository 支付记录仓储实现
type paymentRecordRepository struct {
	db *gorm.DB
}

// NewPaymentRecordRepository 创建支付记录仓储
func NewPaymentRecordRepository(db *gorm.DB) PaymentRecordRepository {
	return &paymentRecordRepository{db: db}
}

// Create 创建支付记录
func (r *paymentRecordRepository) Create(ctx context.Context, record *model.PaymentRecord) error {
	return r.db.WithContext(ctx).Create(record).Error
}

// --- RefundRecordRepository ---

// RefundRecordRepository 退款记录仓储接口
type RefundRecordRepository interface {
	Create(ctx context.Context, record *model.RefundRecord) error
	GetByRefundNo(ctx context.Context, refundNo string) (*model.RefundRecord, error)
	GetByOutRefundNo(ctx context.Context, outRefundNo string) (*model.RefundRecord, error)
}

// refundRecordRepository 退款记录仓储实现
type refundRecordRepository struct {
	db *gorm.DB
}

// NewRefundRecordRepository 创建退款记录仓储
func NewRefundRecordRepository(db *gorm.DB) RefundRecordRepository {
	return &refundRecordRepository{db: db}
}

// Create 创建退款记录
func (r *refundRecordRepository) Create(ctx context.Context, record *model.RefundRecord) error {
	return r.db.WithContext(ctx).Create(record).Error
}

// GetByRefundNo 根据退款单号获取退款记录
func (r *refundRecordRepository) GetByRefundNo(ctx context.Context, refundNo string) (*model.RefundRecord, error) {
	var record model.RefundRecord
	err := r.db.WithContext(ctx).Where("refund_no = ?", refundNo).First(&record).Error
	return &record, err
}

// GetByOutRefundNo 根据第三方退款单号获取退款记录
func (r *refundRecordRepository) GetByOutRefundNo(ctx context.Context, outRefundNo string) (*model.RefundRecord, error) {
	var record model.RefundRecord
	err := r.db.WithContext(ctx).Where("out_refund_no = ?", outRefundNo).First(&record).Error
	return &record, err
}

// --- NotifyLogRepository ---

// NotifyLogRepository 回调日志仓储接口
type NotifyLogRepository interface {
	Create(ctx context.Context, log *model.NotifyLog) error
	GetByOrderNo(ctx context.Context, orderNo string) ([]*model.NotifyLog, error)
	Update(ctx context.Context, log *model.NotifyLog) error
}

// notifyLogRepository 回调日志仓储实现
type notifyLogRepository struct {
	db *gorm.DB
}

// NewNotifyLogRepository 创建回调日志仓储
func NewNotifyLogRepository(db *gorm.DB) NotifyLogRepository {
	return &notifyLogRepository{db: db}
}

// Create 创建回调日志
func (r *notifyLogRepository) Create(ctx context.Context, log *model.NotifyLog) error {
	return r.db.WithContext(ctx).Create(log).Error
}

// GetByOrderNo 根据订单号获取回调日志
func (r *notifyLogRepository) GetByOrderNo(ctx context.Context, orderNo string) ([]*model.NotifyLog, error) {
	var logs []*model.NotifyLog
	err := r.db.WithContext(ctx).Where("order_no = ?", orderNo).Order("created_at DESC").Find(&logs).Error
	return logs, err
}

// Update 更新回调日志
func (r *notifyLogRepository) Update(ctx context.Context, log *model.NotifyLog) error {
	return r.db.WithContext(ctx).Save(log).Error
}
