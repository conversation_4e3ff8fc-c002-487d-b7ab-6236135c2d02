package repository

import (
	"context"

	"pay-core/internal/model"

	"gorm.io/gorm"
)

// ReconciliationRepository 对账仓储接口
type ReconciliationRepository interface {
	CreateBatch(ctx context.Context, batch *model.ReconciliationBatch) error
	GetBatchByBatchNo(ctx context.Context, batchNo string) (*model.ReconciliationBatch, error)
	UpdateBatch(ctx context.Context, batch *model.ReconciliationBatch) error
	CreateDetail(ctx context.Context, detail *model.ReconciliationDetail) error
	ListDetailsByBatchID(ctx context.Context, batchID uint64) ([]*model.ReconciliationDetail, error)
}

// reconciliationRepository 对账仓储实现
type reconciliationRepository struct {
	db *gorm.DB
}

// NewReconciliationRepository 创建对账仓储
func NewReconciliationRepository(db *gorm.DB) ReconciliationRepository {
	return &reconciliationRepository{db: db}
}

// CreateBatch 创建对账批次
func (r *reconciliationRepository) CreateBatch(ctx context.Context, batch *model.ReconciliationBatch) error {
	return r.db.WithContext(ctx).Create(batch).Error
}

// GetBatchByBatchNo 根据批次号获取对账批次
func (r *reconciliationRepository) GetBatchByBatchNo(ctx context.Context, batchNo string) (*model.ReconciliationBatch, error) {
	var batch model.ReconciliationBatch
	err := r.db.WithContext(ctx).Where("batch_no = ?", batchNo).First(&batch).Error
	return &batch, err
}

// UpdateBatch 更新对账批次
func (r *reconciliationRepository) UpdateBatch(ctx context.Context, batch *model.ReconciliationBatch) error {
	return r.db.WithContext(ctx).Save(batch).Error
}

// CreateDetail 创建对账明细
func (r *reconciliationRepository) CreateDetail(ctx context.Context, detail *model.ReconciliationDetail) error {
	return r.db.WithContext(ctx).Create(detail).Error
}

// ListDetailsByBatchID 获取对账明细列表
func (r *reconciliationRepository) ListDetailsByBatchID(ctx context.Context, batchID uint64) ([]*model.ReconciliationDetail, error) {
	var details []*model.ReconciliationDetail
	err := r.db.WithContext(ctx).Where("batch_id = ?", batchID).Order("created_at ASC").Find(&details).Error
	return details, err
}
