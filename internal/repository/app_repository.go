package repository

import (
	"context"

	"pay-core/internal/model"

	"gorm.io/gorm"
)

// AppRepository 应用仓储接口
type AppRepository interface {
	Create(ctx context.Context, app *model.MerchantApp) error
	GetByID(ctx context.Context, id uint64) (*model.MerchantApp, error)
	GetByAppCode(ctx context.Context, appCode string) (*model.MerchantApp, error)
	Update(ctx context.Context, app *model.MerchantApp) error
	Delete(ctx context.Context, id uint64) error
	List(ctx context.Context, req *model.AppListRequest) ([]*model.MerchantApp, int64, error)
}

// appRepository 应用仓储实现
type appRepository struct {
	db *gorm.DB
}

// NewAppRepository 创建应用仓储
func NewAppRepository(db *gorm.DB) AppRepository {
	return &appRepository{db: db}
}

// Create 创建应用
func (r *appRepository) Create(ctx context.Context, app *model.MerchantApp) error {
	return r.db.WithContext(ctx).Create(app).Error
}

// GetByID 根据ID获取应用
func (r *appRepository) GetByID(ctx context.Context, id uint64) (*model.MerchantApp, error) {
	var app model.MerchantApp
	err := r.db.WithContext(ctx).First(&app, id).Error
	if err != nil {
		return nil, err
	}
	return &app, nil
}

// GetByAppCode 根据AppCode获取应用
func (r *appRepository) GetByAppCode(ctx context.Context, appCode string) (*model.MerchantApp, error) {
	var app model.MerchantApp
	err := r.db.WithContext(ctx).Where("app_code = ?", appCode).First(&app).Error
	if err != nil {
		return nil, err
	}
	return &app, nil
}

// Update 更新应用
func (r *appRepository) Update(ctx context.Context, app *model.MerchantApp) error {
	return r.db.WithContext(ctx).Save(app).Error
}

// Delete 删除应用
func (r *appRepository) Delete(ctx context.Context, id uint64) error {
	return r.db.WithContext(ctx).Delete(&model.MerchantApp{}, id).Error
}

// List 获取应用列表
func (r *appRepository) List(ctx context.Context, req *model.AppListRequest) ([]*model.MerchantApp, int64, error) {
	var apps []*model.MerchantApp
	var total int64

	query := r.db.WithContext(ctx).Model(&model.MerchantApp{})

	// 状态过滤
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 关键词搜索
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		query = query.Where("app_name LIKE ? OR app_code LIKE ?", keyword, keyword)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}
	offset := (page - 1) * pageSize

	// 获取列表
	err := query.
		Offset(offset).
		Limit(pageSize).
		Order("created_at DESC").
		Find(&apps).Error

	return apps, total, err
}

