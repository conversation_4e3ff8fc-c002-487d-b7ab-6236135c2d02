package handler

// Response 通用响应结构
type Response struct {
	Code    int         `json:"code"`    // 响应码
	Message string      `json:"message"` // 响应消息
	Data    interface{} `json:"data"`    // 响应数据
}

// SuccessResponse 成功响应
func SuccessResponse(data interface{}) Response {
	return Response{
		Code:    200,
		Message: "Success",
		Data:    data,
	}
}

// ErrorResponse 错误响应
func ErrorResponse(code int, message string) Response {
	return Response{
		Code:    code,
		Message: message,
		Data:    nil,
	}
}

// ValidationErrorResponse 参数验证错误响应
func ValidationErrorResponse(message string) Response {
	return Response{
		Code:    400,
		Message: message,
		Data:    nil,
	}
}

// UnauthorizedResponse 未授权响应
func UnauthorizedResponse() Response {
	return Response{
		Code:    401,
		Message: "Unauthorized",
		Data:    nil,
	}
}

// ForbiddenResponse 禁止访问响应
func ForbiddenResponse() Response {
	return Response{
		Code:    403,
		Message: "Forbidden",
		Data:    nil,
	}
}

// NotFoundResponse 资源不存在响应
func NotFoundResponse(message string) Response {
	if message == "" {
		message = "Resource not found"
	}
	return Response{
		Code:    404,
		Message: message,
		Data:    nil,
	}
}

// InternalErrorResponse 内部错误响应
func InternalErrorResponse() Response {
	return Response{
		Code:    500,
		Message: "Internal server error",
		Data:    nil,
	}
}

// PaginationResponse 分页响应数据
type PaginationResponse struct {
	List     interface{} `json:"list"`      // 数据列表
	Total    int64       `json:"total"`     // 总数
	Page     int         `json:"page"`      // 当前页
	PageSize int         `json:"page_size"` // 每页数量
}

// NewPaginationResponse 创建分页响应
func NewPaginationResponse(list interface{}, total int64, page, pageSize int) PaginationResponse {
	return PaginationResponse{
		List:     list,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}
}
