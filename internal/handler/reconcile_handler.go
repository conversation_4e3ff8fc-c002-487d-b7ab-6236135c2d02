package handler

import (
	"net/http"
	"strconv"
	"time"

	"pay-core/internal/service"

	"github.com/gin-gonic/gin"
)

// ReconciliationHandler 对账处理器
type ReconciliationHandler struct {
	reconciliationService service.ReconciliationService
}

// NewReconciliationHandler 创建对账处理器
func NewReconciliationHandler(reconciliationService service.ReconciliationService) *ReconciliationHandler {
	return &ReconciliationHandler{
		reconciliationService: reconciliationService,
	}
}

// StartReconciliationRequest 开始对账请求
type StartReconciliationRequest struct {
	Date    string `json:"date" binding:"required"`    // 对账日期 YYYY-MM-DD
	Channel string `json:"channel" binding:"required"` // 支付渠道
}

// StartReconciliation 开始对账
// @Summary 开始对账
// @Description 启动指定日期和渠道的对账任务
// @Tags 对账管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body StartReconciliationRequest true "对账参数"
// @Success 200 {object} Response{data=model.ReconciliationBatch}
// @Failure 400 {object} Response
// @Router /api/v1/reconciliation/start [post]
func (h *ReconciliationHandler) StartReconciliation(c *gin.Context) {
	var req StartReconciliationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ValidationErrorResponse("Invalid request parameters"))
		return
	}

	date, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		c.JSON(http.StatusBadRequest, ValidationErrorResponse("Invalid date format, should be YYYY-MM-DD"))
		return
	}

	batch, err := h.reconciliationService.StartReconciliation(c.Request.Context(), date, req.Channel)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse(400, err.Error()))
		return
	}

	c.JSON(http.StatusOK, SuccessResponse(batch))
}

// GetBatch 获取对账批次详情
// @Summary 获取对账批次详情
// @Description 根据批次号获取对账批次详情
// @Tags 对账管理
// @Produce json
// @Security ApiKeyAuth
// @Param batchNo path string true "批次号"
// @Success 200 {object} Response{data=model.ReconciliationBatch}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Router /api/v1/reconciliation/batches/{batchNo} [get]
func (h *ReconciliationHandler) GetBatch(c *gin.Context) {
	batchNo := c.Param("batchNo")
	if batchNo == "" {
		c.JSON(http.StatusBadRequest, ValidationErrorResponse("Batch number is required"))
		return
	}

	batch, err := h.reconciliationService.GetBatch(c.Request.Context(), batchNo)
	if err != nil {
		c.JSON(http.StatusNotFound, NotFoundResponse("Reconciliation batch not found"))
		return
	}

	c.JSON(http.StatusOK, SuccessResponse(batch))
}

// GetDetails 获取对账明细列表
// @Summary 获取对账明细列表
// @Description 获取指定批次的对账明细列表
// @Tags 对账管理
// @Produce json
// @Security ApiKeyAuth
// @Param batch_id query int true "批次ID"
// @Success 200 {object} Response{data=[]model.ReconciliationDetail}
// @Failure 400 {object} Response
// @Router /api/v1/reconciliation/details [get]
func (h *ReconciliationHandler) GetDetails(c *gin.Context) {
	batchIDStr := c.Query("batch_id")
	batchID, err := strconv.ParseUint(batchIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, ValidationErrorResponse("Invalid batch ID"))
		return
	}

	details, err := h.reconciliationService.GetDetails(c.Request.Context(), batchID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse(400, err.Error()))
		return
	}

	c.JSON(http.StatusOK, SuccessResponse(details))
}
