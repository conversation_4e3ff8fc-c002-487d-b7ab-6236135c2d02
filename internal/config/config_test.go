package config

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestConfig_Validate 测试配置验证
func TestConfig_Validate(t *testing.T) {
	tests := []struct {
		name    string
		config  Config
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid minimal config",
			config: Config{
				Database: DatabaseConfig{
					Host:     "localhost",
					Username: "root",
					Database: "test",
				},
			},
			wantErr: false,
		},
		{
			name: "missing database host",
			config: Config{
				Database: DatabaseConfig{
					Username: "root",
					Database: "test",
				},
			},
			wantErr: true,
			errMsg:  "database host is required",
		},
		{
			name: "missing database username",
			config: Config{
				Database: DatabaseConfig{
					Host:     "localhost",
					Database: "test",
				},
			},
			wantErr: true,
			errMsg:  "database username is required",
		},
		{
			name: "missing database name",
			config: Config{
				Database: DatabaseConfig{
					Host:     "localhost",
					Username: "root",
				},
			},
			wantErr: true,
			errMsg:  "database name is required",
		},
		{
			name: "valid alipay config",
			config: Config{
				Database: DatabaseConfig{
					Host:     "localhost",
					Username: "root",
					Database: "test",
				},
				Payment: PaymentConfig{
					Alipay: AlipayConfig{
						AppID: "2021000000000000",
						PrivateKey: `-----BEGIN RSA PRIVATE KEY-----
MIIEpAIBAAKCAQEA...
-----END RSA PRIVATE KEY-----`,
						PublicKey: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A...
-----END PUBLIC KEY-----`,
						IsProd: false,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "alipay missing private key",
			config: Config{
				Database: DatabaseConfig{
					Host:     "localhost",
					Username: "root",
					Database: "test",
				},
				Payment: PaymentConfig{
					Alipay: AlipayConfig{
						AppID:     "2021000000000000",
						PublicKey: "-----BEGIN PUBLIC KEY-----\ntest\n-----END PUBLIC KEY-----",
					},
				},
			},
			wantErr: true,
			errMsg:  "alipay private_key is required when app_id is set",
		},
		{
			name: "alipay invalid private key format",
			config: Config{
				Database: DatabaseConfig{
					Host:     "localhost",
					Username: "root",
					Database: "test",
				},
				Payment: PaymentConfig{
					Alipay: AlipayConfig{
						AppID:      "2021000000000000",
						PrivateKey: "invalid_private_key",
						PublicKey:  "-----BEGIN PUBLIC KEY-----\ntest\n-----END PUBLIC KEY-----",
					},
				},
			},
			wantErr: true,
			errMsg:  "alipay private_key format is invalid",
		},
		{
			name: "valid wechat V3 config",
			config: Config{
				Database: DatabaseConfig{
					Host:     "localhost",
					Username: "root",
					Database: "test",
				},
				Payment: PaymentConfig{
					Wechat: WechatConfig{
						AppID:      "wx1234567890abcdef",
						MchID:      "1234567890",
						APIv3Key:   "12345678901234567890123456789012",
						SerialNo:   "1234567890ABCDEF1234567890ABCDEF12345678",
						PrivateKey: "-----BEGIN PRIVATE KEY-----\ntest\n-----END PRIVATE KEY-----",
					},
				},
			},
			wantErr: false,
		},

		{
			name: "wechat missing mch_id",
			config: Config{
				Database: DatabaseConfig{
					Host:     "localhost",
					Username: "root",
					Database: "test",
				},
				Payment: PaymentConfig{
					Wechat: WechatConfig{
						AppID:    "wx1234567890abcdef",
						APIv3Key: "12345678901234567890123456789012",
					},
				},
			},
			wantErr: true,
			errMsg:  "wechat mch_id is required when app_id is set",
		},
		{
			name: "wechat invalid apiv3_key length",
			config: Config{
				Database: DatabaseConfig{
					Host:     "localhost",
					Username: "root",
					Database: "test",
				},
				Payment: PaymentConfig{
					Wechat: WechatConfig{
						AppID:    "wx1234567890abcdef",
						MchID:    "1234567890",
						APIv3Key: "short_key",
					},
				},
			},
			wantErr: true,
			errMsg:  "wechat apiv3_key must be 32 characters long",
		},
		{
			name: "wechat V3 missing serial_no",
			config: Config{
				Database: DatabaseConfig{
					Host:     "localhost",
					Username: "root",
					Database: "test",
				},
				Payment: PaymentConfig{
					Wechat: WechatConfig{
						AppID:      "wx1234567890abcdef",
						MchID:      "1234567890",
						APIv3Key:   "12345678901234567890123456789012",
						PrivateKey: "-----BEGIN PRIVATE KEY-----\ntest\n-----END PRIVATE KEY-----",
					},
				},
			},
			wantErr: true,
			errMsg:  "wechat serial_no is required",
		},
		{
			name: "wechat V3 missing private key",
			config: Config{
				Database: DatabaseConfig{
					Host:     "localhost",
					Username: "root",
					Database: "test",
				},
				Payment: PaymentConfig{
					Wechat: WechatConfig{
						AppID:    "wx1234567890abcdef",
						MchID:    "1234567890",
						APIv3Key: "12345678901234567890123456789012",
						SerialNo: "1234567890ABCDEF1234567890ABCDEF12345678",
					},
				},
			},
			wantErr: true,
			errMsg:  "wechat private key is required",
		},
		{
			name: "wechat no API key configured",
			config: Config{
				Database: DatabaseConfig{
					Host:     "localhost",
					Username: "root",
					Database: "test",
				},
				Payment: PaymentConfig{
					Wechat: WechatConfig{
						AppID: "wx1234567890abcdef",
						MchID: "1234567890",
					},
				},
			},
			wantErr: true,
			errMsg:  "wechat apiv3_key is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestDatabaseConfig_GetDSN 测试数据库DSN生成
func TestDatabaseConfig_GetDSN(t *testing.T) {
	config := DatabaseConfig{
		Host:     "localhost",
		Port:     3306,
		Username: "root",
		Password: "password",
		Database: "test_db",
		Charset:  "utf8mb4",
	}

	expected := "root:password@tcp(localhost:3306)/test_db?charset=utf8mb4&parseTime=True&loc=Local"
	actual := config.GetDSN()

	assert.Equal(t, expected, actual)
}

// TestPaymentConfig_Validation 测试支付配置验证的边界情况
func TestPaymentConfig_Validation(t *testing.T) {
	baseConfig := Config{
		Database: DatabaseConfig{
			Host:     "localhost",
			Username: "root",
			Database: "test",
		},
	}

	t.Run("empty payment config should pass", func(t *testing.T) {
		config := baseConfig
		err := config.Validate()
		assert.NoError(t, err)
	})

	t.Run("alipay with empty app_id should pass", func(t *testing.T) {
		config := baseConfig
		config.Payment.Alipay.AppID = ""
		config.Payment.Alipay.PrivateKey = "some_key"
		err := config.Validate()
		assert.NoError(t, err)
	})

	t.Run("wechat with empty app_id should pass", func(t *testing.T) {
		config := baseConfig
		config.Payment.Wechat.AppID = ""
		config.Payment.Wechat.MchID = "123"
		err := config.Validate()
		assert.NoError(t, err)
	})
}
