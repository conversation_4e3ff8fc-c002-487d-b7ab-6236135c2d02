package middleware

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"pay-core/internal/repository"
	"pay-core/pkg/logger"
	"pay-core/pkg/types"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

// Logger 日志中间件
func Logger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		logger.WithFields(logrus.Fields{
			"status_code": param.StatusCode,
			"latency":     param.Latency,
			"client_ip":   param.ClientIP,
			"method":      param.Method,
			"path":        param.Path,
			"user_agent":  param.Request.UserAgent(),
			"error":       param.ErrorMessage,
		}).Info("HTTP Request")
		return ""
	})
}

// Recovery 恢复中间件
func Recovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		logger.WithField("panic", recovered).Error("Panic recovered")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Internal server error",
		})
	})
}

// CORS 跨域中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// MerchantAuth 商户认证中间件
func MerchantAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		apiKey := c.GetHeader("X-API-Key")
		if apiKey == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "Missing API key",
			})
			c.Abort()
			return
		}

		// TODO: 验证API Key的有效性
		// 这里应该从数据库查询商户信息
		// 暂时跳过验证，实际项目中需要实现

		c.Set("api_key", apiKey)
		c.Next()
	}
}

// RateLimit 限流中间件
func RateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: 实现限流逻辑
		// 可以使用Redis实现分布式限流
		c.Next()
	}
}

// GetAPIKey 从上下文中获取API Key
func GetAPIKey(c *gin.Context) string {
	if apiKey, exists := c.Get("api_key"); exists {
		if key, ok := apiKey.(string); ok {
			return key
		}
	}
	return ""
}

// ClientInfo 客户端信息
type ClientInfo struct {
	ID        string // 完整ID: APP_001_user123
	AppCode   string // 应用编码: APP_001
	AppUserId string // 应用用户ID: user123
}

// ClientAuth 客户端签名认证中间件
func ClientAuth(rdb *redis.Client, appRepo repository.AppRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 获取请求头
		clientID := c.GetHeader("X-API-Key")
		timestamp := c.GetHeader("X-Timestamp")
		nonce := c.GetHeader("X-Nonce")
		signature := c.GetHeader("X-Signature")

		// 2. 基础验证
		if clientID == "" || timestamp == "" || nonce == "" || signature == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    40001,
				"message": "Missing required headers",
			})
			c.Abort()
			return
		}

		// 3. 解析客户端ID
		clientInfo := parseClientID(clientID)

		// 4. 验证AppCode是否存在
		if !validateAppCode(c.Request.Context(), appRepo, clientInfo.AppCode) {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    40005,
				"message": "Invalid app code",
			})
			c.Abort()
			return
		}

		// 5. 验证时间戳
		if !validateTimestamp(timestamp) {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    40002,
				"message": "Request expired",
			})
			c.Abort()
			return
		}

		// 6. 验证Nonce（防重放）
		if !validateNonce(rdb, clientID, nonce) {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    40003,
				"message": "Nonce already used",
			})
			c.Abort()
			return
		}

		// 7. 验证签名
		if !validateSignature(c, clientID, timestamp, nonce, signature) {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    40004,
				"message": "Invalid signature",
			})
			c.Abort()
			return
		}

		// 8. 存储到上下文
		c.Set("client_id", clientID)
		c.Set("client_info", clientInfo)

		c.Next()
	}
}

// parseClientID 解析客户端ID
func parseClientID(clientID string) *ClientInfo {
	parts := strings.Split(clientID, "_")
	if len(parts) < 2 {
		return &ClientInfo{
			ID:        clientID,
			AppCode:   "unknown",
			AppUserId: clientID,
		}
	}

	return &ClientInfo{
		ID:        clientID,
		AppCode:   parts[0],
		AppUserId: strings.Join(parts[1:], "_"),
	}
}

// validateAppCode 验证应用编码是否存在
func validateAppCode(ctx context.Context, appRepo repository.AppRepository, appCode string) bool {
	if appCode == "unknown" {
		return false
	}

	app, err := appRepo.GetByAppCode(ctx, appCode)
	if err != nil {
		return false
	}

	return app != nil && app.Status
}

// validateTimestamp 验证时间戳
func validateTimestamp(timestampStr string) bool {
	timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
	if err != nil {
		return false
	}

	now := types.Now().Unix()
	tolerance := viper.GetInt64("auth.timestamp_tolerance")
	if tolerance == 0 {
		tolerance = 300 // 默认5分钟
	}

	return abs(now-timestamp) <= tolerance
}

// abs 绝对值函数
func abs(x int64) int64 {
	if x < 0 {
		return -x
	}
	return x
}

// validateNonce 验证Nonce（防重放攻击）
func validateNonce(rdb *redis.Client, clientID, nonce string) bool {
	ctx := context.Background()
	key := fmt.Sprintf("nonce:%s:%s", clientID, nonce)

	// 检查是否存在
	exists := rdb.Exists(ctx, key).Val()
	if exists > 0 {
		return false // nonce已使用
	}

	// 设置nonce，TTL为10分钟
	ttlStr := viper.GetString("auth.nonce_ttl")
	ttl, err := time.ParseDuration(ttlStr)
	if err != nil || ttl == 0 {
		ttl = 10 * time.Minute // 默认10分钟
	}
	rdb.Set(ctx, key, "1", ttl)
	return true
}

// validateSignature 验证签名
func validateSignature(c *gin.Context, clientID, timestamp, nonce, signature string) bool {
	// 1. 收集所有参数
	params := make(map[string]string)

	// URL参数
	for key, values := range c.Request.URL.Query() {
		if len(values) > 0 {
			params[key] = values[0]
		}
	}

	// POST参数（如果是POST请求）
	if c.Request.Method == "POST" || c.Request.Method == "PUT" {
		// 读取body内容
		body, err := io.ReadAll(c.Request.Body)
		if err == nil && len(body) > 0 {
			// 重新设置body供后续使用
			c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

			// 调试日志：打印请求体
			logger.WithField("request_body", string(body)).Info("Request body received")

			// 解析JSON参数
			var jsonParams map[string]interface{}
			if err := json.Unmarshal(body, &jsonParams); err == nil {
				for key, value := range jsonParams {
					params[key] = fmt.Sprintf("%v", value)
				}
				// 调试日志：打印解析的参数
				logger.WithField("parsed_params", params).Info("Parsed JSON params")
			} else {
				logger.WithField("error", err).Error("Failed to parse JSON body")
			}
		}
	}

	// 2. 添加认证参数
	params["api_key"] = clientID
	params["timestamp"] = timestamp
	params["nonce"] = nonce

	// 3. 生成签名
	expectedSignature := generateSignature(params)

	// 调试日志：打印签名信息
	logger.WithFields(logrus.Fields{
		"client_signature":   signature,
		"expected_signature": expectedSignature,
		"params":            params,
		"sign_string":       buildSignString(params),
	}).Info("Signature validation")

	// 4. 比较签名
	return expectedSignature == signature
}

// generateSignature 生成签名
func generateSignature(params map[string]string) string {
	// 1. 获取所有键并排序
	keys := make([]string, 0, len(params))
	for key := range params {
		if key != "signature" && params[key] != "" {
			keys = append(keys, key)
		}
	}
	sort.Strings(keys)

	// 2. 构建签名字符串
	var parts []string
	for _, key := range keys {
		parts = append(parts, fmt.Sprintf("%s=%s", key, params[key]))
	}

	appSecret := viper.GetString("auth.app_secret")
	if appSecret == "" {
		appSecret = "default_secret" // 默认密钥，生产环境必须配置
	}

	signStr := strings.Join(parts, "&") + "&key=" + appSecret

	// 3. 计算HMAC-SHA256
	h := hmac.New(sha256.New, []byte(appSecret))
	h.Write([]byte(signStr))

	return hex.EncodeToString(h.Sum(nil))
}

// buildSignString 构建签名字符串（用于调试）
func buildSignString(params map[string]string) string {
	// 1. 获取所有键并排序
	keys := make([]string, 0, len(params))
	for key := range params {
		if key != "signature" && params[key] != "" {
			keys = append(keys, key)
		}
	}
	sort.Strings(keys)

	// 2. 构建签名字符串
	var parts []string
	for _, key := range keys {
		parts = append(parts, fmt.Sprintf("%s=%s", key, params[key]))
	}

	appSecret := viper.GetString("auth.app_secret")
	if appSecret == "" {
		appSecret = "default_secret" // 默认密钥，生产环境必须配置
	}

	return strings.Join(parts, "&") + "&key=" + appSecret
}

// GetClientID 从上下文中获取客户端ID
func GetClientID(c *gin.Context) string {
	if clientID, exists := c.Get("client_id"); exists {
		if id, ok := clientID.(string); ok {
			return id
		}
	}
	return ""
}

// GetClientInfo 从上下文中获取客户端信息
func GetClientInfo(c *gin.Context) *ClientInfo {
	if clientInfo, exists := c.Get("client_info"); exists {
		if info, ok := clientInfo.(*ClientInfo); ok {
			return info
		}
	}
	return nil
}

// GetAppCode 从上下文中获取应用编码
func GetAppCode(c *gin.Context) string {
	if clientInfo := GetClientInfo(c); clientInfo != nil {
		return clientInfo.AppCode
	}
	return ""
}

// GetAppUserId 从上下文中获取应用用户ID
func GetAppUserId(c *gin.Context) string {
	if clientInfo := GetClientInfo(c); clientInfo != nil {
		return clientInfo.AppUserId
	}
	return ""
}
