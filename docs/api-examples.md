# API 使用示例

## 基础信息

- **Base URL**: `http://localhost:8080`
- **Content-Type**: `application/json`
- **认证方式**: API签名认证（HMAC-SHA256）

## 认证说明

本系统使用API签名认证，需要在请求头中包含以下认证信息：
- `X-API-Key`: 客户端ID，格式为 `{appCode}_{appUserId}`
  - `appCode`: 应用编码，用于标识接入的应用
  - `appUserId`: 应用用户ID，用于标识应用内的用户
- `X-Timestamp`: 时间戳
- `X-Nonce`: 随机数（防重放）
- `X-Signature`: 签名

**示例**: `X-API-Key: APP_001_user123`

详细的签名生成方法请参考 `examples/` 目录下的客户端SDK示例。

## 支付接口

### 1. 创建支付订单

```bash
curl -X POST http://localhost:8080/api/v1/orders \
  -H "Content-Type: application/json" \
  -H "X-API-Key: APP_001_user123" \
  -H "X-Timestamp: 1640995200" \
  -H "X-Nonce: random_string" \
  -H "X-Signature: generated_signature" \
  -d '{
    "amount": 100.50,
    "payment_channel": "alipay",
    "payment_method": "native"
  }'
```

**说明**:
- `X-API-Key` 中的 `APP_001` 是应用编码，`user123` 是应用用户ID
- 系统会自动从 `X-API-Key` 中提取应用编码和用户ID
- 创建的订单会自动关联到对应的应用和用户

**可选字段说明**:
- `merchant_order_no`: 商户订单号（可选）。如不提供，系统将自动生成
- `subject`: 商品标题（可选）。如不提供，默认为"超级语音交互充值{amount}元"

**完整示例（包含可选字段）**:
```bash
curl -X POST http://localhost:8080/api/v1/orders \
  -H "Content-Type: application/json" \
  -H "X-API-Key: APP_001_user123" \
  -H "X-Timestamp: 1640995200" \
  -H "X-Nonce: random_string" \
  -H "X-Signature: generated_signature" \
  -d '{
    "merchant_order_no": "ORDER_123456",
    "subject": "自定义商品标题",
    "amount": 100.50,
    "body": "这是一个测试订单",
    "payment_channel": "alipay",
    "payment_method": "native"
  }'
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "order_no": "PAY20240101001",
    "app_order_no": "PAY20240101001",
    "amount": 100.50,
    "subject": "超级语音交互充值100.50元",
    "status": "waiting_pay",
    "pay_url": "https://qr.alipay.com/bax08431...",
    "created_at": "2024-01-01T10:00:00Z"
  }
}
```

### 2. 查询支付订单

```bash
curl -X GET http://localhost:8080/api/v1/orders/PAY20240101001 \
  -H "X-API-Key: APP_001_user123" \
  -H "X-Timestamp: 1640995200" \
  -H "X-Nonce: random_string" \
  -H "X-Signature: generated_signature"
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "order_no": "PAY20240101001",
    "app_order_no": "PAY20240101001",
    "amount": 100.50,
    "subject": "超级语音交互充值100.50元",
    "status": "paid",
    "payment_method": "native",
    "payment_channel": "alipay",
    "transaction_id": "2024010122001234567890123456",
    "paid_at": "2024-01-01T10:05:00Z",
    "created_at": "2024-01-01T10:00:00Z"
  }
}
```

### 3. 申请退款

```bash
curl -X POST http://localhost:8080/api/v1/orders/PAY20240101001/refund \
  -H "Content-Type: application/json" \
  -H "X-API-Key: APP_001_user123" \
  -H "X-Timestamp: 1640995200" \
  -H "X-Nonce: random_string" \
  -H "X-Signature: generated_signature" \
  -d '{
    "refund_amount": 50.25,
    "refund_reason": "用户申请退款"
  }'
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "refund_no": "REF20240101001",
    "order_no": "PAY20240101001",
    "refund_amount": 50.25,
    "status": "processing",
    "created_at": "2024-01-01T10:10:00Z"
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 40001 | 缺少必要的认证头 |
| 40002 | 请求已过期 |
| 40003 | Nonce已被使用 |
| 40004 | 签名验证失败 |
| 40005 | 无效的应用编码 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 错误响应格式

所有接口在出错时都会返回统一的错误格式：

```json
{
  "code": 400,
  "message": "错误描述",
  "data": null
}
```

## SDK 示例

详细的客户端SDK示例请参考 `examples/` 目录：
- `client_sdk.go` - Go语言客户端SDK
- `README_Java_Client.md` - Java客户端SDK

这些SDK包含了完整的签名生成和API调用示例。
