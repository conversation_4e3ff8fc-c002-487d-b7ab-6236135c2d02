# 机器人账户服务回调功能指南

## 概述

当支付成功后，PayCore系统会自动调用机器人账户服务的充值接口，更新用户的账户余额。该功能完全按照提供的认证规范实现，并且所有业务回调都会记录到 `notify_logs` 表中。

## 功能特性

### ✅ 已实现功能

1. **支付成功自动回调** - 支付成功后自动调用机器人服务
2. **标准认证实现** - 完全按照 `APIKeyGenerator.java` 的认证规范
3. **完整日志记录** - 所有回调请求和响应都记录到 `notify_logs`
4. **错误处理机制** - 回调失败不影响支付主流程
5. **配置化管理** - 支持灵活的服务配置

## 认证机制

### API Key 生成算法

按照 `docs/APIKeyGenerator.java` 的实现：

```
1. payload = deviceSN (即本服务解析出的 appUserID)
2. signature = HMAC-SHA256(payload, sharedKey)
3. finalData = payload + ":" + signature
4. apiKey = Base64URLEncode(finalData)
```

### 关键参数说明

- **deviceSN**: 等于本服务header认证解析出的 `appUserID`
- **sharedKey**: 固定值 `"default-shared-key-change-in-production"`
- **order_id**: 等于本服务的 `order_no`

## 接口调用

### 目标接口

```bash
curl --location --request POST 'http://chat.backend.efrobot.com/api/v1/robot/recharge' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer {generated_api_key}' \
--data-raw '{
    "amount": "100.00",
    "order_id": "order_20240115_001",
    "description": "账户充值"
}'
```

### 请求参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| amount | string | 充值金额 | "100.00" |
| order_id | string | 订单号（本服务的order_no） | "PAY20240101001" |
| description | string | 充值描述 | "账户充值" |

### 响应格式

```json
{
    "code": 0,
    "message": "success",
    "data": {
        "success": true,
        "order_id": "PAY20240101001"
    }
}
```

## 业务流程

### 支付成功回调流程

1. **接收支付平台回调** → 记录到 `notify_logs` (类型: PAYMENT, 来源: PLATFORM)
2. **更新本地订单状态** → 更新 `payment_orders` 和 `payment_records`
3. **调用机器人服务** → 发送充值请求到机器人账户服务
4. **记录业务回调日志** → 记录到 `notify_logs` (类型: RECHARGE, 来源: APP)

### 数据流转

```
微信支付 → PayCore → 机器人账户服务
   ↓           ↓            ↓
notify_logs  订单更新    账户充值
(PAYMENT)   (状态变更)   (余额更新)
   ↓
notify_logs
(RECHARGE)
```

## 配置说明

### 配置文件 (config.yaml)

```yaml
# 机器人账户服务配置
robot:
  base_url: "http://chat.backend.efrobot.com"
  shared_key: "default-shared-key-change-in-production"
  timeout: 30
```

### 配置参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| base_url | 机器人服务基础URL | - |
| shared_key | 认证共享密钥 | "default-shared-key-change-in-production" |
| timeout | 请求超时时间（秒） | 30 |

## 日志记录

### notify_logs 表记录

所有机器人服务回调都会记录到 `notify_logs` 表：

```sql
INSERT INTO notify_logs (
    order_no,
    app_id,
    notify_type,
    notify_source,
    notify_url,
    request_body,
    response_code,
    response_body,
    status,
    error_msg,
    created_at
) VALUES (
    'PAY20240101001',
    1,
    'RECHARGE',
    'APP',
    'http://chat.backend.efrobot.com/api/v1/robot/recharge',
    '{"amount":"100.00","order_id":"PAY20240101001","description":"账户充值"}',
    200,
    '{"code":0,"message":"success","data":{"success":true,"order_id":"PAY20240101001"}}',
    'SUCCESS',
    '',
    '2024-01-01 10:00:00'
);
```

### 日志字段说明

- **notify_type**: `RECHARGE` (充值回调)
- **notify_source**: `APP` (业务回调)
- **status**: `SUCCESS`/`FAILED`
- **request_body**: 完整的请求JSON
- **response_body**: 完整的响应JSON

## 错误处理

### 错误场景处理

1. **AppUserID为空** - 记录错误日志，跳过机器人服务调用
2. **API Key生成失败** - 记录到 notify_logs，状态为 FAILED
3. **网络请求失败** - 记录错误信息，不影响支付主流程
4. **机器人服务返回错误** - 记录响应内容和错误码

### 容错机制

- 机器人服务调用失败**不会影响**支付主流程
- 所有错误都会详细记录到日志中
- 支持重试机制（可配置）

## 测试验证

### 测试文件

使用 `test/test_robot_callback.go` 进行功能测试：

```bash
go run test/test_robot_callback.go
```

### 测试内容

1. **API Key生成验证** - 验证认证算法正确性
2. **请求构建测试** - 验证请求格式和参数
3. **完整流程测试** - 模拟支付成功后的回调流程

## 监控和排查

### 日志查询

查询机器人服务回调日志：

```sql
-- 查询所有充值回调
SELECT * FROM notify_logs 
WHERE notify_type = 'RECHARGE' 
ORDER BY created_at DESC;

-- 查询失败的充值回调
SELECT * FROM notify_logs 
WHERE notify_type = 'RECHARGE' 
AND status = 'FAILED'
ORDER BY created_at DESC;

-- 查询特定订单的所有回调
SELECT * FROM notify_logs 
WHERE order_no = 'PAY20240101001'
ORDER BY created_at ASC;
```

### 常见问题排查

1. **回调未触发** - 检查 AppUserID 是否为空
2. **认证失败** - 验证 shared_key 配置是否正确
3. **网络超时** - 检查 timeout 配置和网络连通性
4. **响应解析失败** - 查看 response_body 字段的具体内容

## 安全考虑

1. **密钥管理** - 生产环境必须更换默认的 shared_key
2. **HTTPS通信** - 生产环境建议使用 HTTPS
3. **请求验证** - 机器人服务应验证API Key的有效性
4. **日志脱敏** - 敏感信息应进行适当脱敏处理

## 扩展功能

### 未来可扩展的功能

1. **重试机制** - 失败时自动重试
2. **批量处理** - 支持批量充值请求
3. **异步处理** - 使用消息队列异步处理
4. **监控告警** - 失败率过高时自动告警
