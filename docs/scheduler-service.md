# 定时任务服务文档

## 📋 功能概述

定时任务服务负责自动处理系统中的定时任务，目前主要功能是定期关闭过期的支付订单。

## 🔧 实现方案

### 技术选型
- **定时任务库**: `github.com/robfig/cron/v3`
- **执行频率**: 每5分钟执行一次
- **超时控制**: 单次执行最长5分钟

### 核心特性
- ✅ **自动启动**: 服务启动时自动开始定时任务
- ✅ **优雅关闭**: 服务关闭时等待当前任务完成
- ✅ **错误处理**: 任务执行失败不影响后续执行
- ✅ **日志记录**: 详细记录任务执行情况

## 📊 任务详情

### 关闭过期订单任务

**执行时间**: 每5分钟执行一次 (`0 */5 * * * *`)

**任务流程**:
1. 查询数据库中的过期订单（状态为待支付且已过期）
2. 批量更新订单状态为"已关闭"
3. 记录处理结果日志

**性能特点**:
- 单次最多处理100个过期订单
- 执行超时时间：5分钟
- 数据库查询使用索引优化

## 🚀 使用方式

### 启动服务
定时任务服务会在应用启动时自动启动：

```go
// 在 main.go 中自动启动
schedulerService := service.NewSchedulerService(paymentService)
if err := schedulerService.Start(); err != nil {
    log.Fatalf("Failed to start scheduler service: %v", err)
}
```

### 停止服务
应用关闭时会自动停止定时任务：

```go
// 优雅关闭
if err := schedulerService.Stop(); err != nil {
    logger.WithError(err).Error("Failed to stop scheduler service")
}
```

## 📈 监控和日志

### 日志示例

**启动日志**:
```
INFO[2024-12-14T10:00:00Z] Scheduler service started
```

**任务执行日志**:
```
INFO[2024-12-14T10:05:00Z] Starting to close expired orders
INFO[2024-12-14T10:05:01Z] Successfully processed expired orders
```

**错误日志**:
```
ERROR[2024-12-14T10:05:00Z] Failed to close expired orders error="database connection failed"
```

### 监控指标

可以通过日志监控以下指标：
- 任务执行频率
- 任务执行耗时
- 处理的过期订单数量
- 错误发生频率

## ⚙️ 配置说明

### 当前配置
- **执行频率**: 5分钟（固定）
- **超时时间**: 5分钟（固定）
- **批处理大小**: 100个订单（在repository层配置）

### 自定义配置（可扩展）

如需自定义配置，可以在配置文件中添加：

```yaml
scheduler:
  close_expired_orders:
    cron: "0 */5 * * * *"  # 每5分钟
    timeout: "5m"          # 超时时间
    batch_size: 100        # 批处理大小
```

## 🔍 故障排查

### 常见问题

1. **定时任务未执行**
   - 检查服务是否正常启动
   - 查看启动日志是否有错误

2. **任务执行失败**
   - 检查数据库连接
   - 查看错误日志详情

3. **性能问题**
   - 监控任务执行时间
   - 检查数据库查询性能

### 调试方法

1. **查看日志**:
   ```bash
   # 查看定时任务相关日志
   grep "Scheduler\|expired orders" /var/log/pay-core.log
   ```

2. **手动触发**:
   ```go
   // 在代码中手动调用
   err := paymentService.CloseExpiredOrders(context.Background())
   ```

## 🚧 扩展计划

### 可能的扩展功能
1. **对账任务**: 定期执行对账操作
2. **数据清理**: 定期清理过期的日志数据
3. **报表生成**: 定期生成业务报表
4. **健康检查**: 定期检查系统健康状态

### 扩展方式
```go
// 添加新的定时任务
_, err := s.cron.AddFunc("0 0 2 * * *", s.dailyReconcile)  // 每天凌晨2点执行对账
_, err := s.cron.AddFunc("0 0 0 * * 0", s.weeklyCleanup)   // 每周日执行数据清理
```

## 📝 总结

简单定时任务方案具有以下优势：
- **实现简单**: 仅20行核心代码
- **稳定可靠**: 使用成熟的cron库
- **资源占用低**: 内存和CPU占用极少
- **维护成本低**: 逻辑清晰，易于调试
- **扩展性好**: 可以轻松添加新的定时任务

该方案完全满足当前的业务需求，同时为未来的功能扩展留下了空间。
