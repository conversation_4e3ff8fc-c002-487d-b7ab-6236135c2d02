openapi: 3.0.3
info:
  title: Pay Core API
  version: 1.0.0
  description: 支付系统核心API，提供支付订单管理、退款、对账等功能
  termsOfService: http://swagger.io/terms/
  contact:
    name: API Support
    url: http://www.swagger.io/support
    email: <EMAIL>
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html

servers:
  - url: http://localhost:8080
    description: 开发环境
  - url: https://api.paycore.com
    description: 生产环境

security:
  - ApiKeyAuth: []

paths:
  /health:
    get:
      tags:
        - 系统监控
      summary: 健康检查
      description: 检查系统运行状态
      security: []
      responses:
        '200':
          description: 系统正常
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "ok"
                  time:
                    type: integer
                    format: int64
                    example: **********

  /api/v1/orders:
    post:
      tags:
        - 支付管理
      summary: 创建支付订单
      description: 为应用创建新的支付订单
      parameters:
        - $ref: '#/components/parameters/TimestampHeader'
        - $ref: '#/components/parameters/NonceHeader'
        - $ref: '#/components/parameters/SignatureHeader'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrderRequest'
      responses:
        '200':
          description: 订单创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                success:
                  value:
                    code: 200
                    message: "Success"
                    data:
                      order_no: "PAY20240101001"
                      total_amount: 100.50
                      pay_info:
                        qr_code: "https://qr.alipay.com/bax08431..."
                        pay_url: "alipays://platformapi/startapp?..."
                      expire_time: "2024-01-01T12:00:00Z"
                      pay_channel: "alipay"
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /api/v1/orders/{order_no}:
    get:
      tags:
        - 支付管理
      summary: 获取订单详情（精确查询）
      description: 根据**系统订单号**精确获取订单的详细信息。这是获取单个资源的标准RESTful接口，性能最优。
      parameters:
        - $ref: '#/components/parameters/TimestampHeader'
        - $ref: '#/components/parameters/NonceHeader'
        - $ref: '#/components/parameters/SignatureHeader'
        - name: order_no
          in: path
          required: true
          description: 订单号
          schema:
            type: string
            example: "PAY20240101001"
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/PaymentOrder'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /api/v1/orders/query:
    get:
      tags:
        - 支付管理
      summary: 查询订单状态（灵活查询）
      description: 根据多种条件灵活查询订单状态。支持**系统订单号(order_no)**、**商户订单号(app_order_no)**或**第三方交易号(out_trade_no)**进行查询（三选一）。
      parameters:
        - $ref: '#/components/parameters/TimestampHeader'
        - $ref: '#/components/parameters/NonceHeader'
        - $ref: '#/components/parameters/SignatureHeader'
        - name: order_no
          in: query
          description: 系统订单号
          schema:
            type: string
        - name: app_order_no
          in: query
          description: 业务订单号
          schema:
            type: string
        - name: out_trade_no
          in: query
          description: 第三方交易号
          schema:
            type: string
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/PaymentOrder'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /api/v1/orders/{order_no}/refund:
    post:
      tags:
        - 支付管理
      summary: 申请退款
      description: 为已支付的订单申请退款
      parameters:
        - $ref: '#/components/parameters/TimestampHeader'
        - $ref: '#/components/parameters/NonceHeader'
        - $ref: '#/components/parameters/SignatureHeader'
        - name: order_no
          in: path
          required: true
          description: 订单号
          schema:
            type: string
            example: "PAY20240101001"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefundOrderRequest'
      responses:
        '200':
          description: 退款申请成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/RefundOrder'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /notify/{channel}:
    post:
      tags:
        - 支付回调
      summary: 支付回调通知
      description: 接收支付渠道的异步回调通知
      security: []
      parameters:
        - name: channel
          in: path
          required: true
          description: 支付渠道
          schema:
            type: string
            enum: [alipay, wechat, qq]
            example: "alipay"
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              additionalProperties: true
          application/json:
            schema:
              type: object
              additionalProperties: true
      responses:
        '200':
          description: 处理成功
          content:
            text/plain:
              schema:
                type: string
                example: "SUCCESS"
        '400':
          description: 处理失败
          content:
            text/plain:
              schema:
                type: string
                example: "FAIL"

  /api/v1/reconciliation/start:
    post:
      tags:
        - 对账管理
      summary: 开始对账
      description: 启动指定日期和渠道的对账任务
      parameters:
        - $ref: '#/components/parameters/TimestampHeader'
        - $ref: '#/components/parameters/NonceHeader'
        - $ref: '#/components/parameters/SignatureHeader'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StartReconciliationRequest'
      responses:
        '200':
          description: 对账任务启动成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ReconciliationBatch'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /api/v1/reconciliation/batches/{batchNo}:
    get:
      tags:
        - 对账管理
      summary: 获取对账批次详情
      description: 根据批次号获取对账批次详情
      parameters:
        - $ref: '#/components/parameters/TimestampHeader'
        - $ref: '#/components/parameters/NonceHeader'
        - $ref: '#/components/parameters/SignatureHeader'
        - name: batchNo
          in: path
          required: true
          description: 批次号
          schema:
            type: string
            example: "BATCH20240101001"
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ReconciliationBatch'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /api/v1/reconciliation/details:
    get:
      tags:
        - 对账管理
      summary: 获取对账明细列表
      description: 获取指定批次的对账明细列表
      parameters:
        - $ref: '#/components/parameters/TimestampHeader'
        - $ref: '#/components/parameters/NonceHeader'
        - $ref: '#/components/parameters/SignatureHeader'
        - name: batch_id
          in: query
          required: true
          description: 批次ID
          schema:
            type: integer
            format: int64
            example: 1
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/ReconciliationDetail'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

components:
  parameters:
    TimestampHeader:
      name: X-Timestamp
      in: header
      required: true
      description: Unix时间戳（秒），用于防止重放攻击。
      schema:
        type: integer
        example: **********
    NonceHeader:
      name: X-Nonce
      in: header
      required: true
      description: 唯一随机字符串，用于防止重放攻击。
      schema:
        type: string
        example: "a1b2c3d4-e5f6-7890-1234-567890abcdef"
    SignatureHeader:
      name: X-Signature
      in: header
      required: true
      description: 使用HMAC-SHA256算法生成的请求签名。
      schema:
        type: string
        example: "generated_signature_string"

  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: |
        API签名认证，客户端需要同时在请求头中提供以下四个字段：
        - `X-API-Key`: 客户端ID，格式为 `{appCode}_{appUserId}`。
        - `X-Timestamp`: Unix时间戳（秒），用于防止重放攻击。
        - `X-Nonce`: 唯一随机字符串，用于防止重放攻击。
        - `X-Signature`: 使用HMAC-SHA256算法生成的请求签名。

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 400
            message: "Invalid request parameters"
            data: null

    Unauthorized:
      description: 未授权访问
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 401
            message: "Unauthorized"
            data: null

    NotFound:
      description: 资源不存在
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 404
            message: "Resource not found"
            data: null

    InternalError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 500
            message: "Internal server error"
            data: null

  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应码
          example: 200
        message:
          type: string
          description: 响应消息
          example: "Success"
        data:
          description: 响应数据
          nullable: true

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误码
        message:
          type: string
          description: 错误消息
        data:
          nullable: true
          description: 错误详情

    CreateOrderRequest:
      type: object
      required:
        - amount
      properties:
        merchant_order_no:
          type: string
          maxLength: 64
          description: 商户订单号（可选，如不提供系统将自动生成）
          example: "ORDER_123456"
        subject:
          type: string
          maxLength: 256
          description: 商品标题（可选，默认为充值描述）
          example: "超级语音交互充值100.50元"
          default: "超级语音交互充值{amount}元"
        body:
          type: string
          maxLength: 512
          description: 商品描述
          example: "这是一个测试订单"
        amount:
          type: number
          format: decimal
          minimum: 0.01
          description: 订单金额（元）
          example: 100.50
        currency:
          type: string
          minLength: 3
          maxLength: 3
          description: 货币代码
          example: "CNY"
          default: "CNY"
        payment_method:
          type: string
          enum: [native, h5, app, mini]
          description: 支付方式（可选）
          example: "native"
          default: "native"
        payment_channel:
          type: string
          enum: [alipay, wechat, qq]
          description: 支付渠道（可选）
          example: "wechat"
          default: "wechat"
        app_user_id:
          type: string
          maxLength: 128
          description: 应用用户ID
          example: "user123"
        expire_minutes:
          type: integer
          minimum: 1
          maximum: 1440
          description: 过期时间（分钟）
          example: 30
          default: 30
        notify_url:
          type: string
          format: uri
          description: 支付结果回调地址
          example: "https://example.com/notify"
        attach:
          type: string
          maxLength: 1024
          description: 附加数据
          example: "custom_data"

    PaymentOrder:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 订单ID
          example: 1
        order_no:
          type: string
          description: 支付系统订单号
          example: "PAY20240101001"
        out_trade_no:
          type: string
          description: 第三方支付订单号
          example: "2024010122001234567890123456"
        app_id:
          type: integer
          format: int64
          description: 应用ID
          example: 1
        app_order_no:
          type: string
          description: 业务方订单号
          example: "ORDER_123456"
        app_user_id:
          type: string
          description: 业务方用户ID
          example: "user123"
        pay_channel:
          type: string
          description: 支付渠道
          example: "alipay"
        total_amount:
          type: number
          format: decimal
          description: 订单金额（元）
          example: 100.50
        subject:
          type: string
          description: 商品标题
          example: "测试商品"
        body:
          type: string
          description: 商品描述
          example: "这是一个测试订单"
        status:
          type: string
          enum: [WAITING_PAY, PAID, CLOSED, REFUNDED]
          description: 订单状态
          example: "PAID"
        expire_time:
          type: string
          format: date-time
          description: 订单过期时间
          example: "2024-01-01T12:00:00Z"
        paid_at:
          type: string
          format: date-time
          nullable: true
          description: 支付完成时间
          example: "2024-01-01T10:05:00Z"
        pay_info:
          type: object
          description: 支付信息
          additionalProperties: true
          example:
            qr_code: "https://qr.alipay.com/bax08431..."
            pay_url: "alipays://platformapi/startapp?..."
        payer_info:
          type: object
          description: 付款方信息
          additionalProperties: true
          example:
            buyer_id: "2088123456789012"
            buyer_logon_id: "test***@example.com"
        app_notify_status:
          type: string
          enum: [PENDING, SUCCESS, FAILED]
          description: 业务回调状态
          example: "SUCCESS"
        attach:
          type: string
          description: 附加数据
          example: "custom_data"
        created_at:
          type: string
          format: date-time
          description: 创建时间
          example: "2024-01-01T10:00:00Z"
        updated_at:
          type: string
          format: date-time
          description: 更新时间
          example: "2024-01-01T10:05:00Z"

    RefundOrderRequest:
      type: object
      required:
        - app_refund_no
        - refund_amount
      properties:
        app_refund_no:
          type: string
          maxLength: 128
          description: 业务方退款单号
          example: "REFUND_123456"
        refund_amount:
          type: number
          format: decimal
          minimum: 0.01
          description: 退款金额（元）
          example: 50.25
        refund_reason:
          type: string
          maxLength: 255
          description: 退款原因
          example: "用户申请退款"

    RefundOrder:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 退款单ID
          example: 1
        refund_no:
          type: string
          description: 退款单号
          example: "REF20240101001"
        order_no:
          type: string
          description: 原支付订单号
          example: "PAY20240101001"
        app_id:
          type: integer
          format: int64
          description: 应用ID
          example: 1
        app_refund_no:
          type: string
          description: 业务方退款单号
          example: "REFUND_123456"
        refund_amount:
          type: number
          format: decimal
          description: 退款金额（元）
          example: 50.25
        refund_reason:
          type: string
          description: 退款原因
          example: "用户申请退款"
        status:
          type: string
          enum: [PROCESSING, SUCCESS, FAILED]
          description: 退款状态
          example: "SUCCESS"
        out_refund_no:
          type: string
          description: 第三方退款单号
          example: "20240101220015123456"
        success_time:
          type: string
          format: date-time
          nullable: true
          description: 退款成功时间
          example: "2024-01-01T10:15:00Z"
        created_at:
          type: string
          format: date-time
          description: 创建时间
          example: "2024-01-01T10:10:00Z"
        updated_at:
          type: string
          format: date-time
          description: 更新时间
          example: "2024-01-01T10:15:00Z"

    StartReconciliationRequest:
      type: object
      required:
        - date
        - channel
      properties:
        date:
          type: string
          format: date
          description: 对账日期 (YYYY-MM-DD)
          example: "2024-01-01"
        channel:
          type: string
          enum: [alipay, wechat, qq]
          description: 支付渠道
          example: "alipay"

    ReconciliationBatch:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 批次ID
          example: 1
        batch_no:
          type: string
          description: 对账批次号
          example: "BATCH20240101001"
        pay_channel:
          type: string
          description: 支付渠道
          example: "alipay"
        reconcile_date:
          type: string
          format: date
          description: 对账日期
          example: "2024-01-01"
        status:
          type: string
          enum: [PROCESSING, SUCCESS, FAILED]
          description: 对账状态
          example: "SUCCESS"
        started_at:
          type: string
          format: date-time
          nullable: true
          description: 开始时间
          example: "2024-01-02T02:00:00Z"
        finished_at:
          type: string
          format: date-time
          nullable: true
          description: 完成时间
          example: "2024-01-02T02:30:00Z"
        created_at:
          type: string
          format: date-time
          description: 创建时间
          example: "2024-01-02T02:00:00Z"
        updated_at:
          type: string
          format: date-time
          description: 更新时间
          example: "2024-01-02T02:30:00Z"

    ReconciliationDetail:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 明细ID
          example: 1
        batch_id:
          type: integer
          format: int64
          description: 对账批次ID
          example: 1
        transaction_type:
          type: string
          enum: [PAYMENT, REFUND]
          description: 交易类型
          example: "PAYMENT"
        transaction_no:
          type: string
          description: 交易单号
          example: "PAY20240101001"
        out_trade_no:
          type: string
          description: 第三方订单号
          example: "2024010122001234567890123456"
        trade_no:
          type: string
          description: 第三方交易号
          example: "2024010122001234567890123456"
        platform_amount:
          type: number
          format: decimal
          description: 平台金额（元）
          example: 100.50
        platform_status:
          type: string
          description: 平台状态
          example: "PAID"
        platform_time:
          type: string
          format: date-time
          nullable: true
          description: 平台交易时间
          example: "2024-01-01T10:05:00Z"
        channel_amount:
          type: number
          format: decimal
          description: 渠道金额（元）
          example: 100.50
        channel_status:
          type: string
          description: 渠道状态
          example: "TRADE_SUCCESS"
        channel_time:
          type: string
          format: date-time
          nullable: true
          description: 渠道交易时间
          example: "2024-01-01T10:05:00Z"
        reconcile_result:
          type: string
          enum: [MATCH, AMOUNT_DIFF, STATUS_DIFF, PLATFORM_ONLY, CHANNEL_ONLY]
          description: 对账结果
          example: "MATCH"
        diff_reason:
          type: string
          description: 差异原因
          example: "金额不匹配"
        created_at:
          type: string
          format: date-time
          description: 创建时间
          example: "2024-01-02T02:15:00Z"

tags:
  - name: 系统监控
    description: 系统状态监控相关接口
  - name: 支付管理
    description: 支付订单管理相关接口
  - name: 支付回调
    description: 支付平台回调通知接口
  - name: 对账管理
    description: 对账相关接口

externalDocs:
  description: 更多文档
  url: https://github.com/your-org/pay-core
