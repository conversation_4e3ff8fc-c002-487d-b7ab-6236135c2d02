-- 通用支付系统简化数据库表结构设计（10张表）

-- 1. 接入应用表（简化版）
CREATE TABLE `merchant_apps` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '应用ID',
    `app_code` varchar(64) NOT NULL COMMENT '应用编码',
    `app_name` varchar(128) NOT NULL COMMENT '应用名称',
    `app_secret` varchar(256) NOT NULL COMMENT '应用密钥',
    `notify_url` varchar(512) DEFAULT NULL COMMENT '支付结果回调地址',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    
    `created_at` datatime NOT NULL DEFAULT CURRENT_datatime,
    `updated_at` datatime NOT NULL DEFAULT CURRENT_datatime ON UPDATE CURRENT_datatime,
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_app_code` (`app_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='接入应用表';

-- 2. 支付订单表（简化版）
CREATE TABLE `payment_orders` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
    `order_no` varchar(64) NOT NULL COMMENT '支付系统订单号',
    `out_trade_no` varchar(64) DEFAULT NULL COMMENT '第三方支付订单号',
    
    -- 接入应用信息
    `app_id` bigint(20) NOT NULL COMMENT '应用ID',
    `app_order_no` varchar(128) NOT NULL COMMENT '业务方订单号',
    `app_user_id` varchar(128) DEFAULT NULL COMMENT '业务方用户ID',
    
    -- 支付信息
    `pay_channel` varchar(32) NOT NULL COMMENT '支付渠道',
    `total_amount` decimal(10,2) NOT NULL COMMENT '订单金额（元）',
    
    -- 商品信息
    `subject` varchar(256) NOT NULL COMMENT '商品标题',
    `body` varchar(512) DEFAULT NULL COMMENT '商品描述',
    
    -- 订单状态
    `status` varchar(32) NOT NULL DEFAULT 'WAITING_PAY' COMMENT '订单状态：WAITING_PAY-待支付，PAID-已支付，CLOSED-已关闭，REFUNDED-已退款',
    
    -- 时间控制
    `expire_time` datatime NOT NULL COMMENT '订单过期时间',
    `paid_at` datatime NULL DEFAULT NULL COMMENT '支付完成时间',
    
    -- 支付凭证
    `pay_info` json DEFAULT NULL COMMENT '支付信息：{"qr_code":"","pay_url":"","prepay_id":""}',
    `payer_info` json DEFAULT NULL COMMENT '付款方信息：{"openid":"","user_id":""}',
    
    -- 业务回调状态
    `app_notify_status` varchar(32) DEFAULT 'PENDING' COMMENT '业务回调状态：PENDING-待通知，SUCCESS-成功，FAILED-失败',
    
    -- 扩展信息
    `attach` varchar(1024) DEFAULT NULL COMMENT '附加数据（透传给业务方）',
    
    -- 审计字段
    `created_at` datatime NOT NULL DEFAULT CURRENT_datatime,
    `updated_at` datatime NOT NULL DEFAULT CURRENT_datatime ON UPDATE CURRENT_datatime,
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    UNIQUE KEY `uk_app_order` (`app_id`, `app_order_no`),
    KEY `idx_out_trade_no` (`out_trade_no`),
    KEY `idx_status` (`status`),
    KEY `idx_pay_channel` (`pay_channel`),
    KEY `idx_created_at` (`created_at`),
    FOREIGN KEY (`app_id`) REFERENCES `merchant_apps` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付订单表';

-- 3. 支付记录表
CREATE TABLE `payment_records` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `order_no` varchar(64) NOT NULL COMMENT '支付系统订单号',
    `out_trade_no` varchar(64) NOT NULL COMMENT '第三方支付订单号',
    `trade_no` varchar(128) DEFAULT NULL COMMENT '第三方平台交易号',
    `pay_channel` varchar(32) NOT NULL COMMENT '支付渠道',
    
    -- 金额信息
    `total_amount` decimal(10,2) NOT NULL COMMENT '订单金额',
    `receipt_amount` decimal(10,2) DEFAULT NULL COMMENT '实收金额',
    `fee_amount` decimal(10,2) DEFAULT NULL COMMENT '手续费',
    
    -- 时间信息
    `trade_time` datatime NOT NULL COMMENT '交易时间',
    
    -- 对账和结算状态
    `reconcile_status` varchar(32) DEFAULT 'PENDING' COMMENT '对账状态：PENDING-待对账，MATCHED-已匹配，DIFF-有差异',
    `settlement_status` varchar(32) DEFAULT 'PENDING' COMMENT '结算状态：PENDING-待结算，SETTLED-已结算',
    `settlement_id` bigint(20) DEFAULT NULL COMMENT '结算记录ID',
    
    `created_at` datatime NOT NULL DEFAULT CURRENT_datatime,
    `updated_at` datatime NOT NULL DEFAULT CURRENT_datatime ON UPDATE CURRENT_datatime,
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_out_trade_no` (`out_trade_no`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_trade_no` (`trade_no`),
    KEY `idx_trade_time` (`trade_time`),
    KEY `idx_reconcile_status` (`reconcile_status`),
    KEY `idx_settlement_status` (`settlement_status`),
    KEY `idx_settlement_id` (`settlement_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付记录表';

-- 4. 退款订单表（简化版）
CREATE TABLE `refund_orders` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `refund_no` varchar(64) NOT NULL COMMENT '退款单号',
    `order_no` varchar(64) NOT NULL COMMENT '原支付订单号',
    `app_id` bigint(20) NOT NULL COMMENT '应用ID',
    `app_refund_no` varchar(128) NOT NULL COMMENT '业务方退款单号',
    
    -- 退款信息
    `refund_amount` decimal(10,2) NOT NULL COMMENT '退款金额',
    `refund_reason` varchar(255) DEFAULT NULL COMMENT '退款原因',
    `status` varchar(32) NOT NULL DEFAULT 'PROCESSING' COMMENT '退款状态：PROCESSING-处理中，SUCCESS-成功，FAILED-失败',
    
    -- 第三方信息
    `out_refund_no` varchar(64) DEFAULT NULL COMMENT '第三方退款单号',
    `success_time` datatime NULL DEFAULT NULL COMMENT '退款成功时间',
    
    `created_at` datatime NOT NULL DEFAULT CURRENT_datatime,
    `updated_at` datatime NOT NULL DEFAULT CURRENT_datatime ON UPDATE CURRENT_datatime,
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_refund_no` (`refund_no`),
    UNIQUE KEY `uk_app_refund` (`app_id`, `app_refund_no`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款订单表';

-- 5. 退款记录表
CREATE TABLE `refund_records` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `refund_no` varchar(64) NOT NULL COMMENT '退款单号',
    `out_refund_no` varchar(64) NOT NULL COMMENT '第三方退款单号',
    `refund_trade_no` varchar(128) DEFAULT NULL COMMENT '第三方退款交易号',
    `pay_channel` varchar(32) NOT NULL COMMENT '支付渠道',

    -- 关联信息
    `order_no` varchar(64) NOT NULL COMMENT '原支付订单号',
    `out_trade_no` varchar(64) DEFAULT NULL COMMENT '原第三方支付订单号',

    -- 金额信息
    `refund_amount` decimal(10,2) NOT NULL COMMENT '申请退款金额',
    `actual_refund_amount` decimal(10,2) DEFAULT NULL COMMENT '实际退款金额',
    `refund_fee` decimal(10,2) DEFAULT NULL COMMENT '退款手续费',

    -- 时间信息
    `refund_time` datatime NOT NULL COMMENT '退款时间',

    -- 对账和结算状态
    `reconcile_status` varchar(32) DEFAULT 'PENDING' COMMENT '对账状态：PENDING-待对账，MATCHED-已匹配，DIFF-有差异',
    `settlement_status` varchar(32) DEFAULT 'PENDING' COMMENT '结算状态：PENDING-待结算，SETTLED-已结算',
    `settlement_id` bigint(20) DEFAULT NULL COMMENT '结算记录ID',

    `created_at` datatime NOT NULL DEFAULT CURRENT_datatime,
    `updated_at` datatime NOT NULL DEFAULT CURRENT_datatime ON UPDATE CURRENT_datatime,

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_out_refund_no` (`out_refund_no`),
    KEY `idx_refund_no` (`refund_no`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_out_trade_no` (`out_trade_no`),
    KEY `idx_refund_time` (`refund_time`),
    KEY `idx_reconcile_status` (`reconcile_status`),
    KEY `idx_settlement_status` (`settlement_status`),
    KEY `idx_settlement_id` (`settlement_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款记录表';

-- 6. 对账批次表（简化版 - 只保留批次管理功能）
CREATE TABLE `reconciliation_batches` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `batch_no` varchar(64) NOT NULL COMMENT '对账批次号',
    `pay_channel` varchar(32) NOT NULL COMMENT '支付渠道',
    `reconcile_date` date NOT NULL COMMENT '对账日期',

    -- 对账状态
    `status` varchar(32) NOT NULL DEFAULT 'PROCESSING' COMMENT '对账状态：PROCESSING-处理中，SUCCESS-成功，FAILED-失败',

    `started_at` datatime NULL DEFAULT NULL COMMENT '开始时间',
    `finished_at` datatime NULL DEFAULT NULL COMMENT '完成时间',
    `created_at` datatime NOT NULL DEFAULT CURRENT_datatime,
    `updated_at` datatime NOT NULL DEFAULT CURRENT_datatime ON UPDATE CURRENT_datatime,

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_batch_no` (`batch_no`),
    KEY `idx_channel_date` (`pay_channel`, `reconcile_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对账批次表';

-- 7. 对账明细表（支持支付和退款对账）
CREATE TABLE `reconciliation_details` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `batch_id` bigint(20) NOT NULL COMMENT '对账批次ID',
    `transaction_type` varchar(32) NOT NULL DEFAULT 'PAYMENT' COMMENT '交易类型：PAYMENT-支付，REFUND-退款',
    `transaction_no` varchar(64) DEFAULT NULL COMMENT '交易单号（支付订单号或退款单号）',
    `out_trade_no` varchar(64) DEFAULT NULL COMMENT '第三方订单号',
    `trade_no` varchar(128) DEFAULT NULL COMMENT '第三方交易号',

    -- 平台数据
    `platform_amount` decimal(10,2) DEFAULT NULL COMMENT '平台金额',
    `platform_status` varchar(32) DEFAULT NULL COMMENT '平台状态',
    `platform_time` datatime NULL DEFAULT NULL COMMENT '平台交易时间',

    -- 第三方数据
    `channel_amount` decimal(10,2) DEFAULT NULL COMMENT '渠道金额',
    `channel_status` varchar(32) DEFAULT NULL COMMENT '渠道状态',
    `channel_time` datatime NULL DEFAULT NULL COMMENT '渠道交易时间',

    -- 对账结果
    `reconcile_result` varchar(32) NOT NULL COMMENT '对账结果：MATCH-匹配，DIFF-差异，PLATFORM_ONLY-平台独有，CHANNEL_ONLY-渠道独有',
    `diff_reason` varchar(255) DEFAULT NULL COMMENT '差异原因',

    `created_at` datatime NOT NULL DEFAULT CURRENT_datatime,

    PRIMARY KEY (`id`),
    KEY `idx_batch_id` (`batch_id`),
    KEY `idx_transaction_no` (`transaction_no`),
    KEY `idx_transaction_type` (`transaction_type`),
    KEY `idx_reconcile_result` (`reconcile_result`),
    FOREIGN KEY (`batch_id`) REFERENCES `reconciliation_batches` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对账明细表';

-- 8. 结算记录表（简化版）
CREATE TABLE `settlement_records` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `settlement_no` varchar(64) NOT NULL COMMENT '结算单号',
    `pay_channel` varchar(32) NOT NULL COMMENT '支付渠道',
    `settlement_date` date NOT NULL COMMENT '结算日期',
    
    -- 结算金额
    `total_amount` decimal(15,2) NOT NULL COMMENT '结算总金额',
    `fee_amount` decimal(15,2) DEFAULT 0.00 COMMENT '手续费金额',
    `actual_amount` decimal(15,2) NOT NULL COMMENT '实际到账金额',
    
    -- 统计信息
    `order_count` int(11) NOT NULL COMMENT '订单笔数',
    
    -- 结算状态
    `status` varchar(32) NOT NULL DEFAULT 'PENDING' COMMENT '结算状态：PENDING-待结算，SETTLED-已结算，FAILED-结算失败',
    `settled_at` datatime NULL DEFAULT NULL COMMENT '结算时间',
    
    `created_at` datatime NOT NULL DEFAULT CURRENT_datatime,
    `updated_at` datatime NOT NULL DEFAULT CURRENT_datatime ON UPDATE CURRENT_datatime,
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_settlement_no` (`settlement_no`),
    KEY `idx_channel_date` (`pay_channel`, `settlement_date`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='结算记录表';

-- 9. 支付渠道配置表（简化版）
CREATE TABLE `payment_channels` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `channel_code` varchar(32) NOT NULL COMMENT '渠道编码：wechat_native,alipay_native',
    `channel_name` varchar(64) NOT NULL COMMENT '渠道名称',
    `provider` varchar(32) NOT NULL COMMENT '支付提供商：WECHAT,ALIPAY',
    `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    
    -- 配置信息
    `app_id` varchar(128) DEFAULT NULL COMMENT '应用ID',
    `mch_id` varchar(128) DEFAULT NULL COMMENT '商户号',
    `api_key` varchar(256) DEFAULT NULL COMMENT 'API密钥',
    `private_key` text DEFAULT NULL COMMENT '私钥',
    
    `created_at` datatime NOT NULL DEFAULT CURRENT_datatime,
    `updated_at` datatime NOT NULL DEFAULT CURRENT_datatime ON UPDATE CURRENT_datatime,
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_channel_code` (`channel_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付渠道配置表';

-- 10. 回调日志表（合并版）
CREATE TABLE `notify_logs` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `order_no` varchar(64) NOT NULL COMMENT '订单号',
    `app_id` bigint(20) DEFAULT NULL COMMENT '应用ID',
    `notify_type` varchar(32) NOT NULL COMMENT '回调类型：PAYMENT-支付，REFUND-退款',
    `notify_source` varchar(32) NOT NULL COMMENT '回调来源：PLATFORM-平台回调，APP-业务回调',

    -- 回调信息
    `notify_url` varchar(512) DEFAULT NULL COMMENT '回调地址',
    `request_body` text NOT NULL COMMENT '请求内容',
    `response_code` int(11) DEFAULT NULL COMMENT '响应状态码',
    `response_body` text DEFAULT NULL COMMENT '响应内容',

    -- 处理状态
    `status` varchar(32) NOT NULL COMMENT '状态：SUCCESS-成功，FAILED-失败，TIMEOUT-超时',
    `retry_count` int(11) DEFAULT 0 COMMENT '重试次数',
    `error_msg` varchar(1024) DEFAULT NULL COMMENT '错误信息',

    `created_at` datatime NOT NULL DEFAULT CURRENT_datatime,

    PRIMARY KEY (`id`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_app_id` (`app_id`),
    KEY `idx_notify_type` (`notify_type`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回调日志表';

-- ========================================
-- 初始化基础数据
-- ========================================

-- 插入支付渠道
INSERT INTO `payment_channels` (`channel_code`, `channel_name`, `provider`) VALUES
('wechat_native', '微信扫码支付', 'WECHAT'),
('wechat_jsapi', '微信公众号支付', 'WECHAT'),
('wechat_h5', '微信H5支付', 'WECHAT'),
('wechat_app', '微信APP支付', 'WECHAT'),
('alipay_native', '支付宝扫码支付', 'ALIPAY'),
('alipay_wap', '支付宝WAP支付', 'ALIPAY'),
('alipay_app', '支付宝APP支付', 'ALIPAY');

-- ========================================
-- 创建性能优化索引
-- ========================================

-- 支付订单表索引优化
CREATE INDEX idx_orders_app_status ON payment_orders(app_id, status, created_at);
CREATE INDEX idx_orders_expire_close ON payment_orders(expire_time, status);

-- 时间相关索引
CREATE INDEX idx_payment_records_time ON payment_records(trade_time DESC);
CREATE INDEX idx_refund_orders_time ON refund_orders(created_at DESC);
CREATE INDEX idx_refund_records_time ON refund_records(refund_time DESC);

-- 对账相关索引
CREATE INDEX idx_reconcile_batch_status ON reconciliation_batches(status, reconcile_date);
CREATE INDEX idx_reconcile_detail_result ON reconciliation_details(reconcile_result, batch_id);

-- 结算记录索引
CREATE INDEX idx_settlement_status_date ON settlement_records(status, settlement_date DESC);

-- ========================================
-- 设计说明
-- ========================================

/*
简化设计说明：

1. 表数量：从12张表简化为10张表
2. 字段数量：从100+字段简化为80+字段
3. 保留功能：支付、退款（含记录）、对账（含明细）、结算、应用管理、渠道配置、回调日志
4. 移除功能：过度复杂的差异处理、系统配置表

主要简化内容：
- 移除了过度设计的字段（allowed_channels、rate_limit等）
- 合并了功能相似的表（两个日志表合并为一个）
- 简化了扩展字段（移除extra_data、remark等）
- 移除了现阶段用不到的复杂功能

新增完善内容：
- 增加了退款记录表（refund_records），保持与支付流程的一致性
- 扩展了对账明细表，支持支付和退款的统一对账
- 完善了关联关系，退款与原支付订单的关联

核心优势：
- 降低了系统复杂度
- 保持了核心业务功能完整
- 支付和退款流程设计一致
- 便于开发和维护
- 支持后续扩展

关系设计：
支付流程：payment_orders → payment_records → reconciliation_details
退款流程：refund_orders → refund_records → reconciliation_details
关联关系：refund_orders.order_no → payment_orders.order_no
          refund_records.order_no → payment_orders.order_no
*/
