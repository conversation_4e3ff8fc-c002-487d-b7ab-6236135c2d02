# API 修改说明 - 创建订单接口优化

## 修改概述

本次修改优化了创建订单接口，将 `merchant_order_no` 和 `subject` 字段改为可选，并提供了智能默认值，提升了API的易用性。

## 修改详情

### 1. 字段变更

#### merchant_order_no（商户订单号）
- **修改前**: 必填字段
- **修改后**: 可选字段
- **默认行为**: 如不提供，系统自动使用生成的系统订单号

#### subject（商品标题）
- **修改前**: 必填字段
- **修改后**: 可选字段
- **默认行为**: 如不提供，自动生成格式为"超级语音交互充值{amount}元"

### 2. 文件修改清单

#### 2.1 OpenAPI文档
**文件**: `docs/openapi.yaml`

- 更新 `CreateOrderRequest` 的 `required` 字段列表
- 移除 `merchant_order_no` 和 `subject` 的必填要求
- 更新字段描述和示例
- 添加 `subject` 的默认值模板

#### 2.2 Go模型定义
**文件**: `internal/model/payment_order.go`

- 修改 `CreateOrderRequest` 结构体
- 将 `MerchantOrderNo` 的 `binding` 标签从 `required` 改为 `omitempty`
- 将 `Subject` 的 `binding` 标签从 `required` 改为 `omitempty`

#### 2.3 业务逻辑实现
**文件**: `internal/service/payment_service.go`

- 在 `CreateOrder` 方法中添加默认值处理逻辑
- `merchantOrderNo` 为空时，使用系统生成的订单号
- `subject` 为空时，使用模板生成默认标题
- 优化订单号重复检查逻辑

#### 2.4 API示例文档
**文件**: `docs/api-examples.md`

- 更新创建订单的示例请求，展示最简化的必填字段
- 添加可选字段的说明和完整示例
- 更新响应示例，反映默认值的使用

## 新的API使用方式

### 最简化请求（推荐）
```json
{
  "amount": 100.50,
  "payment_channel": "alipay",
  "payment_method": "native"
}
```

**系统行为**:
- `merchant_order_no`: 自动设置为系统订单号（如：PAY20240101001）
- `subject`: 自动设置为"超级语音交互充值100.50元"

### 完整请求（自定义）
```json
{
  "merchant_order_no": "ORDER_123456",
  "subject": "自定义商品标题",
  "amount": 100.50,
  "body": "这是一个测试订单",
  "payment_channel": "alipay",
  "payment_method": "native"
}
```

## 兼容性说明

### 向后兼容
- ✅ 现有客户端代码无需修改
- ✅ 仍然支持显式提供 `merchant_order_no` 和 `subject`
- ✅ 所有现有功能保持不变

### 新功能特性
- ✅ 支持更简化的API调用
- ✅ 智能默认值生成
- ✅ 减少客户端代码复杂度

## 业务逻辑变更

### 订单号处理逻辑
```
if merchant_order_no 为空:
    merchant_order_no = 系统生成的订单号
else:
    检查 merchant_order_no 是否重复
    如果重复，返回错误
```

### 商品标题处理逻辑
```
if subject 为空:
    subject = "超级语音交互充值{amount}元"
else:
    使用提供的 subject
```

## 测试建议

### 1. 基础功能测试
- [ ] 测试仅提供必填字段的订单创建
- [ ] 验证默认 `subject` 格式正确
- [ ] 验证 `merchant_order_no` 自动生成

### 2. 兼容性测试
- [ ] 测试提供完整字段的订单创建
- [ ] 验证自定义 `merchant_order_no` 重复检查
- [ ] 验证自定义 `subject` 正常使用

### 3. 边界情况测试
- [ ] 测试 `amount` 为不同数值时的 `subject` 格式
- [ ] 测试空字符串与 null 值的处理
- [ ] 测试并发创建订单的唯一性

## 示例对比

### 修改前（必须提供所有字段）
```bash
curl -X POST http://localhost:8080/api/v1/orders \
  -H "Content-Type: application/json" \
  -d '{
    "merchant_order_no": "ORDER_123456",  # 必填
    "subject": "测试商品",                # 必填
    "amount": 100.50,
    "payment_channel": "alipay",
    "payment_method": "native"
  }'
```

### 修改后（最简化调用）
```bash
curl -X POST http://localhost:8080/api/v1/orders \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 100.50,
    "payment_channel": "alipay",
    "payment_method": "native"
  }'
```

## 响应示例

### 使用默认值的响应
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "order_no": "PAY20240101001",
    "app_order_no": "PAY20240101001",
    "amount": 100.50,
    "subject": "超级语音交互充值100.50元",
    "status": "waiting_pay",
    "pay_info": {
      "qr_code": "https://qr.alipay.com/bax08431...",
      "pay_url": "alipays://platformapi/startapp?..."
    },
    "expire_time": "2024-01-01T12:00:00Z",
    "pay_channel": "alipay"
  }
}
```

## 优势总结

1. **简化集成**: 客户端只需提供核心的支付信息
2. **智能默认**: 系统自动生成合理的默认值
3. **向后兼容**: 不影响现有客户端的使用
4. **用户友好**: 默认的商品标题更符合业务场景
5. **减少错误**: 减少客户端需要处理的字段，降低出错概率

## 注意事项

1. **订单号唯一性**: 系统生成的订单号保证全局唯一
2. **金额格式**: `subject` 中的金额保留两位小数
3. **字符长度**: 生成的 `subject` 符合字段长度限制
4. **并发安全**: 订单号生成算法保证并发安全

---

**修改日期**: 2024年1月  
**版本**: v1.1.0  
**影响范围**: 创建订单接口 (`POST /api/v1/orders`)
