# 部署指南

## 快速开始

### 使用 Docker Compose（推荐）

1. **克隆项目**
```bash
git clone <repository-url>
cd pay-core
```

2. **启动服务**
```bash
# 启动所有服务（MySQL、Redis、应用）
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看应用日志
docker-compose logs -f app
```

3. **验证服务**
```bash
# 健康检查
curl http://localhost:8080/health

# 应该返回：{"status":"ok","time":**********}
```

4. **测试API**
```bash
# 运行测试脚本
chmod +x scripts/test.sh
./scripts/test.sh
```

### 本地开发环境

1. **环境要求**
- Go 1.21+
- MySQL 8.0+
- Redis 7.0+

2. **启动依赖服务**
```bash
# 只启动数据库和Redis
docker-compose up -d mysql redis

# 等待服务启动
sleep 10
```

3. **初始化数据库**
```bash
# 执行数据库初始化脚本
make migrate
```

4. **安装依赖**
```bash
make deps
```

5. **运行应用**
```bash
make run
```

## 配置说明

### 环境变量配置

支持通过环境变量覆盖配置文件：

```bash
# 数据库配置
export PAY_CORE_DATABASE_HOST=localhost
export PAY_CORE_DATABASE_PORT=3306
export PAY_CORE_DATABASE_USERNAME=root
export PAY_CORE_DATABASE_PASSWORD=123456
export PAY_CORE_DATABASE_DATABASE=pay_core

# Redis配置
export PAY_CORE_REDIS_ADDR=localhost:6379
export PAY_CORE_REDIS_PASSWORD=""
export PAY_CORE_REDIS_DB=0

# 支付配置
export PAY_CORE_PAYMENT_ALIPAY_APP_ID="your_app_id"
export PAY_CORE_PAYMENT_ALIPAY_PRIVATE_KEY="your_private_key"
export PAY_CORE_PAYMENT_ALIPAY_PUBLIC_KEY="your_public_key"

# 认证配置
export PAY_CORE_AUTH_APP_SECRET="your_app_secret"
```

### 配置文件

创建生产环境配置文件 `configs/config.prod.yaml`：

```yaml
server:
  port: 8080
  mode: release

database:
  host: your-db-host
  port: 3306
  username: your-db-user
  password: your-db-password
  database: pay_core
  charset: utf8mb4
  max_idle_conns: 20
  max_open_conns: 200
  conn_max_lifetime: 3600

redis:
  addr: your-redis-host:6379
  password: your-redis-password
  db: 0

payment:
  alipay:
    app_id: "your_production_app_id"
    private_key: |
      -----BEGIN RSA PRIVATE KEY-----
      your_production_private_key
      -----END RSA PRIVATE KEY-----
    public_key: |
      -----BEGIN PUBLIC KEY-----
      your_production_public_key
      -----END PUBLIC KEY-----
    is_prod: true

  wechat:
    app_id: "your_production_app_id"
    mch_id: "your_production_mch_id"
    api_key: "your_production_api_key"
    is_prod: true

log:
  level: info
  format: json
  output: stdout

auth:
  app_secret: "your_production_app_secret"
  timestamp_tolerance: 300
  nonce_ttl: 600s
```

## 生产环境部署

### 1. 使用 Docker

```bash
# 构建镜像
docker build -t pay-core:v1.0.0 .

# 运行容器
docker run -d \
  --name pay-core \
  -p 8080:8080 \
  -e PAY_CORE_DATABASE_HOST=your-db-host \
  -e PAY_CORE_DATABASE_PASSWORD=your-db-password \
  -e PAY_CORE_REDIS_ADDR=your-redis-host:6379 \
  -v /path/to/config.prod.yaml:/root/configs/config.yaml \
  pay-core:v1.0.0
```

### 2. 使用 Kubernetes

创建 `deployments/k8s/deployment.yaml`：

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pay-core
  labels:
    app: pay-core
spec:
  replicas: 3
  selector:
    matchLabels:
      app: pay-core
  template:
    metadata:
      labels:
        app: pay-core
    spec:
      containers:
      - name: pay-core
        image: pay-core:v1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: PAY_CORE_DATABASE_HOST
          value: "mysql-service"
        - name: PAY_CORE_REDIS_ADDR
          value: "redis-service:6379"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: pay-core-service
spec:
  selector:
    app: pay-core
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
```

部署到 Kubernetes：

```bash
kubectl apply -f deployments/k8s/
```

### 3. 使用 Systemd

创建服务文件 `/etc/systemd/system/pay-core.service`：

```ini
[Unit]
Description=Pay Core Service
After=network.target

[Service]
Type=simple
User=paycore
WorkingDirectory=/opt/pay-core
ExecStart=/opt/pay-core/bin/pay-core
Restart=always
RestartSec=5
Environment=PAY_CORE_DATABASE_HOST=localhost
Environment=PAY_CORE_DATABASE_PASSWORD=your_password

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable pay-core
sudo systemctl start pay-core
sudo systemctl status pay-core
```

## 监控和日志

### 1. 日志收集

使用 ELK Stack 或类似工具收集日志：

```yaml
# docker-compose.yml 中添加
  filebeat:
    image: docker.elastic.co/beats/filebeat:7.15.0
    volumes:
      - ./logs:/var/log/app
      - ./filebeat.yml:/usr/share/filebeat/filebeat.yml
    depends_on:
      - app
```

### 2. 监控指标

集成 Prometheus 监控：

```go
// 在 main.go 中添加
import "github.com/prometheus/client_golang/prometheus/promhttp"

// 添加监控端点
r.GET("/metrics", gin.WrapH(promhttp.Handler()))
```

### 3. 健康检查

应用已提供健康检查端点：

```bash
curl http://localhost:8080/health
```

## 安全配置

### 1. HTTPS 配置

使用 Nginx 作为反向代理：

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 2. 防火墙配置

```bash
# 只允许必要的端口
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw deny 8080/tcp   # 应用端口不直接暴露
sudo ufw enable
```

### 3. 数据库安全

```sql
-- 创建专用数据库用户
CREATE USER 'paycore'@'%' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON pay_core.* TO 'paycore'@'%';
FLUSH PRIVILEGES;
```

## 性能优化

### 1. 数据库优化

```sql
-- 添加必要的索引
CREATE INDEX idx_orders_created_at ON payment_orders(created_at);
CREATE INDEX idx_orders_status_channel ON payment_orders(status, payment_channel);

-- 配置 MySQL
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
max_connections = 200
```

### 2. Redis 优化

```conf
# redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 3. 应用优化

```yaml
# 增加连接池配置
database:
  max_idle_conns: 50
  max_open_conns: 200
  conn_max_lifetime: 3600
```

## 备份策略

### 1. 数据库备份

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -h localhost -u root -p123456 pay_core > backup_${DATE}.sql
aws s3 cp backup_${DATE}.sql s3://your-backup-bucket/
```

### 2. 定时备份

```bash
# 添加到 crontab
0 2 * * * /path/to/backup.sh
```

## 故障排查

### 1. 常见问题

**数据库连接失败**
```bash
# 检查数据库连接
mysql -h localhost -u root -p -e "SELECT 1"

# 检查应用日志
docker-compose logs app | grep -i error
```

**Redis 连接失败**
```bash
# 检查 Redis 连接
redis-cli ping

# 检查 Redis 配置
redis-cli config get "*"
```

### 2. 日志分析

```bash
# 查看错误日志
grep -i error /var/log/pay-core/app.log

# 查看性能日志
grep -i "slow" /var/log/pay-core/app.log
```

### 3. 性能监控

```bash
# 检查系统资源
top
htop
iostat -x 1

# 检查网络连接
netstat -tulpn | grep :8080
```

## 升级指南

### 1. 滚动升级

```bash
# 构建新版本
docker build -t pay-core:v1.1.0 .

# 逐个更新容器
docker-compose up -d --no-deps app
```

### 2. 数据库迁移

```bash
# 备份数据库
mysqldump pay_core > backup_before_upgrade.sql

# 执行迁移脚本
mysql pay_core < migrations/v1.1.0.sql
```

这个部署指南涵盖了从开发环境到生产环境的完整部署流程，包括配置、监控、安全和维护等各个方面。
