# Pay Core API OpenAPI 文档说明

## 概述

本文档描述了支付系统核心API的OpenAPI 3.0规范，包含了所有支付相关的接口定义、数据模型和认证方式。

## 文档位置

- **OpenAPI YAML文件**: `docs/openapi.yaml`
- **在线文档**: 可以通过Swagger UI或其他OpenAPI工具查看

## 如何使用OpenAPI文档

### 1. 在线查看

您可以使用以下工具查看和测试API：

#### Swagger UI
```bash
# 使用Docker运行Swagger UI
docker run -p 8081:8080 -e SWAGGER_JSON=/openapi.yaml -v $(pwd)/docs/openapi.yaml:/openapi.yaml swaggerapi/swagger-ui
```

然后访问 `http://localhost:8081` 查看API文档。

#### Swagger Editor
访问 [Swagger Editor](https://editor.swagger.io/) 并导入 `docs/openapi.yaml` 文件。

### 2. 生成客户端SDK

使用OpenAPI Generator生成各种语言的客户端SDK：

```bash
# 安装OpenAPI Generator
npm install @openapitools/openapi-generator-cli -g

# 生成Java客户端
openapi-generator-cli generate -i docs/openapi.yaml -g java -o ./generated/java-client

# 生成Python客户端
openapi-generator-cli generate -i docs/openapi.yaml -g python -o ./generated/python-client

# 生成Go客户端
openapi-generator-cli generate -i docs/openapi.yaml -g go -o ./generated/go-client

# 生成JavaScript客户端
openapi-generator-cli generate -i docs/openapi.yaml -g javascript -o ./generated/js-client
```

### 3. 生成服务端代码

```bash
# 生成Spring Boot服务端代码
openapi-generator-cli generate -i docs/openapi.yaml -g spring -o ./generated/spring-server

# 生成Node.js Express服务端代码
openapi-generator-cli generate -i docs/openapi.yaml -g nodejs-express-server -o ./generated/node-server
```

## API认证说明

### 签名认证

本系统使用HMAC-SHA256签名认证，需要在请求头中包含以下信息：

| 请求头 | 说明 | 示例 |
|--------|------|------|
| X-API-Key | 客户端ID，格式：{appCode}_{appUserId} | APP_001_user123 |
| X-Timestamp | 时间戳（秒） | 1640995200 |
| X-Nonce | 随机数（防重放） | random_string_123 |
| X-Signature | 签名值 | generated_signature |

### 签名生成步骤

1. 构造待签名字符串：
   ```
   method + "\n" + uri + "\n" + query_string + "\n" + timestamp + "\n" + nonce + "\n" + body
   ```

2. 使用HMAC-SHA256算法和应用密钥对待签名字符串进行签名

3. 将签名结果进行Base64编码

详细的签名实现请参考 `examples/` 目录下的客户端SDK示例。

## API接口分组

### 1. 系统监控
- `GET /health` - 健康检查

### 2. 支付管理
- `POST /api/v1/orders` - 创建支付订单
- `GET /api/v1/orders/{order_no}` - 获取订单信息
- `GET /api/v1/orders/query` - 查询订单状态
- `POST /api/v1/orders/{order_no}/refund` - 申请退款

### 3. 支付回调
- `POST /notify/{channel}` - 支付回调通知

### 4. 对账管理
- `POST /api/v1/reconciliation/start` - 开始对账
- `GET /api/v1/reconciliation/batches/{batchNo}` - 获取对账批次详情
- `GET /api/v1/reconciliation/details` - 获取对账明细列表

## 数据模型

### 核心实体

1. **PaymentOrder** - 支付订单
2. **RefundOrder** - 退款订单
3. **ReconciliationBatch** - 对账批次
4. **ReconciliationDetail** - 对账明细

### 请求模型

1. **CreateOrderRequest** - 创建订单请求
2. **RefundOrderRequest** - 退款请求
3. **StartReconciliationRequest** - 开始对账请求

### 响应模型

1. **ApiResponse** - 通用API响应
2. **ErrorResponse** - 错误响应

## 状态码说明

### 订单状态
- `WAITING_PAY` - 待支付
- `PAID` - 已支付
- `CLOSED` - 已关闭
- `REFUNDED` - 已退款

### 退款状态
- `PROCESSING` - 处理中
- `SUCCESS` - 成功
- `FAILED` - 失败

### 对账状态
- `PROCESSING` - 处理中
- `SUCCESS` - 成功
- `FAILED` - 失败

### 对账结果
- `MATCH` - 匹配
- `AMOUNT_DIFF` - 金额差异
- `STATUS_DIFF` - 状态差异
- `PLATFORM_ONLY` - 仅平台有记录
- `CHANNEL_ONLY` - 仅渠道有记录

## 支付渠道

目前支持的支付渠道：
- `alipay` - 支付宝
- `wechat` - 微信支付
- `qq` - QQ钱包

## 支付方式

- `native` - 扫码支付
- `h5` - H5支付
- `app` - APP支付
- `mini` - 小程序支付

## 错误处理

所有API错误都遵循统一的错误响应格式：

```json
{
  "code": 400,
  "message": "错误描述",
  "data": null
}
```

常见错误码：
- `400` - 请求参数错误
- `401` - 未授权访问
- `404` - 资源不存在
- `500` - 服务器内部错误

## 开发建议

1. **使用生成的客户端SDK** - 推荐使用OpenAPI Generator生成的客户端SDK，可以减少开发工作量并确保API调用的正确性。

2. **错误处理** - 请妥善处理API返回的错误信息，特别是认证相关的错误。

3. **幂等性** - 支付相关的操作具有幂等性，相同的请求参数会返回相同的结果。

4. **回调处理** - 支付回调是异步的，请确保正确处理回调通知。

5. **测试环境** - 建议在测试环境充分测试后再部署到生产环境。

## 更新日志

- **v1.0.0** - 初始版本，包含基础的支付、退款、对账功能

## 联系我们

如有问题或建议，请通过以下方式联系我们：
- Email: <EMAIL>
- 文档: http://www.swagger.io/support
