# 数据库设计

## 1. 数据库概述

支付系统采用 MySQL 作为主数据库，Redis 作为缓存层。数据库设计遵循第三范式，确保数据一致性和完整性。

## 2. 数据库架构

### 2.1 主从架构
- **Master**: 处理写操作
- **Slave**: 处理读操作，提供数据备份
- **Redis**: 缓存热点数据，提升查询性能

### 2.2 分库分表策略
- 按业务模块分库
- 大表按时间或ID分表
- 读写分离优化性能

## 3. 核心数据表设计
docs/02-simplified-database-design.sql

## 4. 索引设计

### 4.1 主键索引
- 所有表都使用自增主键 `id`
- 确保查询性能和数据唯一性

### 4.2 唯一索引
- 订单号、商户号等业务唯一字段
- 防止重复数据插入

### 4.3 普通索引
- 查询频繁的字段：状态、时间、商户ID等
- 组合索引优化复合查询

### 4.4 外键约束
- 逻辑外键，不使用物理外键
- 保证数据一致性的同时提升性能

## 5. 数据安全

### 5.1 敏感数据加密
- API密钥使用AES加密存储
- 用户密码使用bcrypt哈希
- 支付信息传输加密

### 5.2 数据备份
- 定期全量备份
- 实时增量备份
- 异地容灾备份

### 5.3 访问控制
- 数据库用户权限分离
- 应用层访问控制
- 审计日志记录

## 6. 性能优化

### 6.1 读写分离
- 主库处理写操作
- 从库处理读操作
- 缓存热点数据

### 6.2 分区分表
- 按时间分区历史数据
- 按业务分表大数据量表
- 定期清理过期数据

### 6.3 缓存策略
- Redis缓存热点数据
- 查询结果缓存
- 分布式缓存一致性
