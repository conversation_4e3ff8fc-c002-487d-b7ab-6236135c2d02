# 自动对账功能实现文档

## 📋 功能概述

自动对账功能已成功集成到定时任务系统中，支持按配置的时间自动执行多渠道对账任务。

## 🚀 核心特性

### ✅ 已实现功能

1. **定时自动对账**
   - 支持配置化的执行时间
   - 多渠道并行对账（支付宝、微信等）
   - 分布式锁防重复执行

2. **灵活配置**
   - 可配置执行时间（cron表达式）
   - 可配置对账渠道
   - 可配置延迟天数
   - 可配置超时时间

3. **高可用性**
   - 分布式锁机制
   - 超时控制
   - 错误处理和日志记录

## ⚙️ 配置说明

### 配置文件示例

```yaml
scheduler:
  daily_reconcile:
    enabled: true                    # 是否启用日对账
    cron: "0 0 2 * * *"             # 每天凌晨2点执行
    timeout_minutes: 30              # 超时时间（分钟）
    channels: ["alipay", "wechat"]   # 对账渠道
    delay_days: 1                    # 对账前一天的数据
    lock_key: "pay_core:daily_reconcile"  # 分布式锁key
    lock_ttl: 1800                   # 锁TTL（秒，30分钟）
```

### 配置参数详解

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | bool | true | 是否启用自动对账 |
| `cron` | string | "0 0 2 * * *" | cron表达式，默认每天凌晨2点 |
| `timeout_minutes` | int | 30 | 单次对账超时时间（分钟） |
| `channels` | []string | ["alipay", "wechat"] | 需要对账的支付渠道 |
| `delay_days` | int | 1 | 对账延迟天数（对账前N天的数据） |
| `lock_key` | string | "pay_core:daily_reconcile" | Redis分布式锁key |
| `lock_ttl` | int | 1800 | 锁的生存时间（秒） |

## 🔄 执行流程

### 自动对账流程图

```mermaid
graph TD
    A[定时任务触发] --> B[获取分布式锁]
    B --> C{锁获取成功?}
    C -->|否| D[跳过执行]
    C -->|是| E[计算对账日期]
    E --> F[遍历配置的渠道]
    F --> G[启动渠道对账]
    G --> H[记录执行结果]
    H --> I{还有其他渠道?}
    I -->|是| F
    I -->|否| J[释放分布式锁]
    J --> K[任务完成]
```

### 详细执行步骤

1. **定时触发**: 根据cron表达式定时触发
2. **获取锁**: 使用Redis分布式锁防止重复执行
3. **计算日期**: 根据delay_days计算对账日期
4. **多渠道对账**: 遍历配置的渠道，逐个执行对账
5. **异步处理**: 每个渠道的对账任务异步执行
6. **结果记录**: 记录对账结果和日志
7. **释放锁**: 安全释放分布式锁

## 📊 对账任务调度

### 常用时间配置

| 场景 | cron表达式 | 说明 |
|------|------------|------|
| **日对账** | `"0 0 2 * * *"` | 每天凌晨2点 |
| **工作日对账** | `"0 0 2 * * 1-5"` | 工作日凌晨2点 |
| **周对账** | `"0 0 3 * * 1"` | 每周一凌晨3点 |
| **月对账** | `"0 0 4 1 * *"` | 每月1号凌晨4点 |
| **测试频率** | `"0 */10 * * * *"` | 每10分钟（仅测试用） |

### 多环境配置建议

#### 开发环境
```yaml
daily_reconcile:
  enabled: true
  cron: "0 */30 * * * *"    # 每30分钟（测试用）
  channels: ["alipay"]       # 只测试一个渠道
  delay_days: 0              # 当天数据
```

#### 生产环境
```yaml
daily_reconcile:
  enabled: true
  cron: "0 0 2 * * *"       # 每天凌晨2点
  channels: ["alipay", "wechat", "qq"]  # 所有渠道
  delay_days: 1             # 前一天数据
```

## 🔒 分布式锁机制

### 锁的作用
- 防止多个服务实例同时执行对账
- 确保对账任务的唯一性
- 避免数据冲突和重复处理

### 锁的实现
```go
// 获取锁
acquired, err := redis.SetNX(ctx, lockKey, lockValue, lockTTL).Result()

// 安全释放锁（Lua脚本）
luaScript := `
    if redis.call("get", KEYS[1]) == ARGV[1] then
        return redis.call("del", KEYS[1])
    else
        return 0
    end
`
redis.Eval(ctx, luaScript, []string{lockKey}, lockValue)
```

### 锁的特性
- ✅ **互斥性**: 同时只有一个实例能获取锁
- ✅ **防死锁**: TTL自动过期机制
- ✅ **安全释放**: 只能释放自己持有的锁
- ✅ **高可用**: 基于Redis的可靠性

## 📈 监控和日志

### 关键日志示例

```bash
# 任务启动
INFO[2024-12-14T02:00:00Z] Acquired distributed lock, starting daily reconcile lock_key=pay_core:daily_reconcile

# 渠道对账开始
INFO[2024-12-14T02:00:01Z] Starting reconcile for channel date=2024-12-13 channel=alipay

# 渠道对账完成
INFO[2024-12-14T02:00:05Z] Successfully started reconcile for channel date=2024-12-13 channel=alipay

# 任务完成
INFO[2024-12-14T02:00:10Z] Daily reconcile task completed
```

### 监控指标

1. **执行频率**: 任务执行次数统计
2. **执行时长**: 单次对账耗时
3. **成功率**: 对账成功/失败比例
4. **锁竞争**: 锁获取成功率
5. **渠道状态**: 各渠道对账状态

## 🧪 测试方法

### 功能测试
```bash
# 运行自动对账测试
go run test_reconcile.go
```

### 手动触发测试
```bash
# 手动触发对账API
curl -X POST http://localhost:8080/api/v1/reconcile/start \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "date": "2024-12-13",
    "channel": "alipay"
  }'
```

### 查看对账结果
```bash
# 获取对账记录列表
curl -X GET http://localhost:8080/api/v1/reconcile/records \
  -H "Authorization: Bearer <token>"

# 获取对账记录详情
curl -X GET http://localhost:8080/api/v1/reconcile/records/{batchNo} \
  -H "Authorization: Bearer <token>"
```

## 🚀 部署和运维

### 启动服务
```bash
# 编译
go build ./cmd/server

# 启动服务
./server
```

### 配置验证
启动时会看到以下日志：
```
INFO[2024-12-14T10:00:00Z] Added daily reconcile job cron="0 0 2 * * *"
INFO[2024-12-14T10:00:00Z] Scheduler service started
```

### 故障排查

1. **对账未执行**
   - 检查 `enabled: true`
   - 验证cron表达式
   - 查看Redis连接

2. **锁获取失败**
   - 检查Redis状态
   - 调整锁TTL时间
   - 查看并发实例数

3. **对账失败**
   - 查看错误日志
   - 检查数据库连接
   - 验证渠道配置

## ✅ 总结

自动对账功能已完全集成到现有的定时任务系统中，具备以下优势：

- 🔄 **自动化**: 无需人工干预，按时自动执行
- ⚙️ **配置化**: 所有参数都可通过配置文件调整
- 🔒 **高可用**: 分布式锁确保任务唯一性
- 📊 **可监控**: 详细的日志和状态记录
- 🧪 **可测试**: 提供完整的测试工具

现在系统支持：
1. ✅ 自动关闭过期订单（每5分钟）
2. ✅ 自动对账任务（每天凌晨2点）
3. ✅ 分布式部署安全
4. ✅ 完全配置化管理

生产环境可以直接使用！🎉
