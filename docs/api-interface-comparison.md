# API 接口对比说明 - 订单查询接口

## 概述

系统中确实存在两个订单查询接口，它们的设计目的和使用场景不同。这种设计遵循了RESTful API的最佳实践，为不同的查询需求提供了专门的接口。

## 接口对比

### 1. GET /api/v1/orders/{order_no} - 获取订单详情

#### 设计目的
- **精确查询**: 通过已知的系统订单号直接获取订单详情
- **RESTful风格**: 符合REST资源定位的标准模式
- **高性能**: 直接通过主键查询，性能最优

#### 参数方式
- **路径参数**: `order_no` 作为URL路径的一部分
- **固定格式**: `/api/v1/orders/PAY20240101001`

#### 查询逻辑
```go
// 直接通过系统订单号查询
order, err := s.paymentOrderRepo.GetByOrderNo(ctx, orderNo)
```

#### 使用场景
- ✅ 客户端已知系统订单号（如创建订单后保存的order_no）
- ✅ 订单详情页面显示
- ✅ 订单状态轮询检查
- ✅ 系统内部服务调用

#### 示例请求
```bash
GET /api/v1/orders/PAY20240101001
```

---

### 2. GET /api/v1/orders/query - 灵活查询订单

#### 设计目的
- **灵活查询**: 支持多种查询条件，适应不同的业务场景
- **兼容性**: 兼容不同系统的订单号格式
- **用户友好**: 用户可能只记得商户订单号或第三方交易号

#### 参数方式
- **查询参数**: 通过URL查询字符串传递参数
- **多选一**: 支持三种不同的查询条件

#### 查询逻辑
```go
// 支持多种查询方式
if req.OrderNo != "" {
    order, err = s.paymentOrderRepo.GetByOrderNo(ctx, req.OrderNo)
} else if req.AppOrderNo != "" {
    order, err = s.paymentOrderRepo.GetByAppOrderNo(ctx, appID, req.AppOrderNo)
} else if req.OutTradeNo != "" {
    order, err = s.paymentOrderRepo.GetByOutTradeNo(ctx, req.OutTradeNo)
}
```

#### 支持的查询条件
1. **order_no**: 系统订单号（与接口1相同）
2. **app_order_no**: 商户订单号（业务方的订单号）
3. **out_trade_no**: 第三方交易号（支付宝、微信等的交易号）

#### 使用场景
- ✅ 客户端只知道商户订单号
- ✅ 通过第三方交易号反查订单
- ✅ 客服系统查询订单
- ✅ 对账系统匹配订单
- ✅ 用户在不同系统间查询订单

#### 示例请求
```bash
# 通过系统订单号查询
GET /api/v1/orders/query?order_no=PAY20240101001

# 通过商户订单号查询
GET /api/v1/orders/query?app_order_no=ORDER_123456

# 通过第三方交易号查询
GET /api/v1/orders/query?out_trade_no=2024010122001234567890123456
```

## 详细对比表

| 特性 | GET /orders/{order_no} | GET /orders/query |
|------|----------------------|-------------------|
| **URL风格** | RESTful路径参数 | 查询字符串参数 |
| **查询条件** | 仅系统订单号 | 三种订单号类型 |
| **性能** | 最优（主键查询） | 根据查询条件而定 |
| **使用复杂度** | 简单 | 稍复杂 |
| **适用场景** | 已知系统订单号 | 多种查询需求 |
| **缓存友好** | 是 | 部分是 |
| **RESTful程度** | 完全符合 | 查询接口风格 |

## 为什么需要两个接口？

### 1. 不同的使用场景

#### 场景A：订单详情页面
```javascript
// 用户点击订单列表中的某个订单，已知order_no
const orderNo = "PAY20240101001";
const response = await fetch(`/api/v1/orders/${orderNo}`);
```

#### 场景B：用户输入商户订单号查询
```javascript
// 用户在查询页面输入自己的订单号
const merchantOrderNo = "ORDER_123456";
const response = await fetch(`/api/v1/orders/query?app_order_no=${merchantOrderNo}`);
```

### 2. API设计最佳实践

#### RESTful资源定位
- `/orders/{id}` 是标准的REST资源访问模式
- 表示"获取特定ID的订单资源"

#### 查询接口模式
- `/orders/query` 是查询接口的常见模式
- 表示"根据条件查询订单"

### 3. 性能考虑

#### 直接访问（接口1）
```sql
SELECT * FROM payment_orders WHERE order_no = 'PAY20240101001';
-- 主键查询，性能最优，可以充分利用索引
```

#### 条件查询（接口2）
```sql
-- 根据不同条件，性能不同
SELECT * FROM payment_orders WHERE app_order_no = 'ORDER_123456' AND app_id = 1;
SELECT * FROM payment_orders WHERE out_trade_no = '2024010122001234567890123456';
```

## 使用建议

### 何时使用 GET /orders/{order_no}
- ✅ 系统内部调用
- ✅ 已知系统订单号的场景
- ✅ 需要最优性能的场景
- ✅ 订单详情页面
- ✅ 定时任务或批处理

### 何时使用 GET /orders/query
- ✅ 用户查询界面
- ✅ 客服系统
- ✅ 对账系统
- ✅ 第三方系统集成
- ✅ 不确定订单号类型的场景

## 代码示例

### 客户端SDK封装建议

```javascript
class PaymentAPI {
  // 直接获取订单（已知系统订单号）
  async getOrder(orderNo) {
    return await this.request(`/api/v1/orders/${orderNo}`);
  }
  
  // 灵活查询订单
  async queryOrder(params) {
    const query = new URLSearchParams(params).toString();
    return await this.request(`/api/v1/orders/query?${query}`);
  }
  
  // 便捷方法：通过商户订单号查询
  async getOrderByMerchantNo(merchantOrderNo) {
    return await this.queryOrder({ app_order_no: merchantOrderNo });
  }
  
  // 便捷方法：通过第三方交易号查询
  async getOrderByTradeNo(outTradeNo) {
    return await this.queryOrder({ out_trade_no: outTradeNo });
  }
}
```

## 总结

这两个接口的存在是合理的，它们各自服务于不同的使用场景：

1. **GET /orders/{order_no}**: 精确、高效的资源访问接口
2. **GET /orders/query**: 灵活、通用的查询接口

这种设计提供了：
- ✅ **最佳性能**: 直接访问接口性能最优
- ✅ **最大灵活性**: 查询接口支持多种条件
- ✅ **良好的用户体验**: 用户可以用任何已知的订单号查询
- ✅ **系统兼容性**: 支持不同系统的订单号格式
- ✅ **RESTful设计**: 符合API设计最佳实践

建议保留这两个接口，它们共同构成了完整的订单查询功能。
