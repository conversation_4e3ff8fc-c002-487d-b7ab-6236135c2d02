# 编译问题修复指南

## 修复的编译错误

### 1. 未使用的导入包
**错误**: `"time" imported and not used`
**修复**: 从 `internal/repository/reconcile_repository.go` 中移除了未使用的 `time` 包导入

### 2. gopay API 变化
由于 gopay 库的 API 发生了变化，需要适配新的接口：

#### 支付宝 API 修复
**错误**: 
- `not enough arguments in call to c.alipayClient.TradePrecreate`
- `aliRsp.Code undefined`
- `undefined: alipay.CodeSuccess`

**修复**:
```go
// 旧版本
aliRsp, err := c.alipayClient.TradePrecreate(bm)
if aliRsp.Code != alipay.CodeSuccess {
    // 处理错误
}

// 新版本
ctx := context.Background()
aliRsp, err := c.alipayClient.TradePrecreate(ctx, bm)
if aliRsp.Response.Code != "10000" {
    // 处理错误
}
```

#### 微信支付 API 修复
**错误**:
- `undefined: gopay.GetRandomString`
- `not enough arguments in call to c.wechatClient.UnifiedOrder`
- `wxRsp.OutTradeNo undefined`

**修复**:
```go
// 添加随机字符串生成函数
func generateRandomString(length int) string {
    bytes := make([]byte, length/2)
    rand.Read(bytes)
    return hex.EncodeToString(bytes)
}

// 修复 API 调用
ctx := context.Background()
wxRsp, err := c.wechatClient.UnifiedOrder(ctx, bm)

// 修复查询 API
wxRsp, _, err := c.wechatClient.QueryOrder(ctx, bm)
```

### 3. JWT 错误修复
**错误**: `undefined: jwt.ErrTokenInvalid`
**修复**: 使用标准错误替代
```go
// 旧版本
return nil, jwt.ErrTokenInvalid

// 新版本
return nil, errors.New("invalid token")
```

### 4. 未使用变量修复
**错误**: `declared and not used: merchantID` 和 `declared and not used: offset`
**修复**: 使用空白标识符忽略未使用的变量
```go
_ = uint64(1) // TODO: 从API Key获取真实的商户ID
_ = (page - 1) * pageSize // offset
```

## 修复后的文件列表

1. `internal/repository/reconcile_repository.go` - 移除未使用的导入
2. `pkg/payment/client.go` - 适配新的 gopay API
3. `internal/middleware/middleware.go` - 修复 JWT 错误
4. `internal/handler/payment_handler.go` - 修复未使用变量

## 编译验证

修复后的项目可以成功编译：

```bash
# 编译成功
go build -o pay-core cmd/server/main.go

# 或使用 Makefile
make build
```

## 运行时依赖

应用需要以下服务才能正常运行：
- MySQL 8.0+
- Redis 7.0+

可以使用 Docker Compose 快速启动依赖服务：

```bash
# 启动依赖服务
docker-compose up -d mysql redis

# 等待服务启动
sleep 10

# 运行应用
./pay-core
```

## 常见问题

### 1. 数据库连接失败
```
failed to connect to database: dial tcp [::1]:3306: connect: connection refused
```
**解决方案**: 确保 MySQL 服务已启动，或使用 Docker Compose 启动

### 2. Redis 连接失败
```
failed to connect to Redis: dial tcp [::1]:6379: connect: connection refused
```
**解决方案**: 确保 Redis 服务已启动

### 3. 配置文件找不到
```
Config File "config" Not Found
```
**解决方案**: 确保 `configs/config.yaml` 文件存在，或设置环境变量

## 开发建议

1. **使用 Makefile**: 项目提供了 Makefile 来简化常用操作
   ```bash
   make deps    # 下载依赖
   make build   # 构建应用
   make run     # 运行应用
   make test    # 运行测试
   ```

2. **使用 Docker Compose**: 推荐使用 Docker Compose 进行开发
   ```bash
   docker-compose up -d  # 启动所有服务
   ```

3. **热重载开发**: 可以使用 air 工具进行热重载开发
   ```bash
   # 安装 air
   go install github.com/cosmtrek/air@latest
   
   # 启动热重载
   air
   ```

## 版本兼容性

- Go 1.21+
- gopay v1.5.100+
- GORM v1.25.5+
- Gin v1.9.1+

确保使用兼容的版本以避免 API 不兼容问题。
