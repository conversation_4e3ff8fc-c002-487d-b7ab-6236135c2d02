# 支付核心系统配置说明

## 配置文件结构

系统使用 YAML 格式的配置文件，主配置文件为 `configs/config.yaml`。

## 支付配置详解

### 支付宝配置

```yaml
payment:
  alipay:
    app_id: "2021000000000000"           # 支付宝应用ID
    private_key: |                       # 应用私钥（RSA2格式）
      -----BEGIN RSA PRIVATE KEY-----
      MIIEpAIBAAKCAQEA...
      -----END RSA PRIVATE KEY-----
    public_key: |                        # 支付宝公钥（用于验签）
      -----BEGIN PUBLIC KEY-----
      MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A...
      -----END PUBLIC KEY-----
    is_prod: false                       # 是否生产环境
```

#### 支付宝配置说明

1. **app_id**: 在支付宝开放平台创建应用后获得
2. **private_key**: 应用私钥，用于签名请求
3. **public_key**: 支付宝公钥，用于验证支付宝返回数据的签名
4. **is_prod**: 
   - `false`: 使用沙箱环境（测试）
   - `true`: 使用生产环境

#### 获取支付宝配置步骤

1. 登录 [支付宝开放平台](https://open.alipay.com/)
2. 创建应用并获取 `app_id`
3. 生成RSA2密钥对，上传公钥到平台
4. 下载支付宝公钥用于验签

### 微信支付V3 API配置

```yaml
payment:
  wechat:
    app_id: "wx1234567890abcdef"         # 微信应用ID
    mch_id: "1234567890"                 # 商户号
    is_prod: false                       # 是否生产环境

    # V3 API必需配置
    apiv3_key: "your_v3_api_key_32_chars"    # V3 API密钥（32位）
    serial_no: "1234567890ABCDEF1234567890ABCDEF12345678"  # 商户证书序列号（40位十六进制）

    # 商户私钥配置（二选一）
    private_key: |                       # 商户私钥内容（PEM格式）
      -----BEGIN PRIVATE KEY-----
      MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
      -----END PRIVATE KEY-----
    private_key_path: "/path/to/apiclient_key.pem"  # 商户私钥文件路径

    # 微信支付平台证书配置（用于验签，可选但推荐）
    platform_cert_path: "/path/to/wechatpay_platform.pem"  # 微信支付平台证书文件路径
    platform_cert: |                    # 微信支付平台证书内容（PEM格式）
      -----BEGIN CERTIFICATE-----
      MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
      -----END CERTIFICATE-----
    platform_cert_serial_no: "1234567890ABCDEF1234567890ABCDEF12345678"  # 微信支付平台证书序列号
```

#### 微信支付V3 API配置说明

1. **app_id**: 微信公众号或小程序的AppID
2. **mch_id**: 微信支付商户号
3. **apiv3_key**: V3 API密钥（32位），在商户平台设置
4. **serial_no**: 商户证书序列号（40位十六进制），从商户证书中获取
5. **商户私钥配置**: 两种方式任选其一
   - **内容方式**: 设置 `private_key`（推荐）
   - **文件路径方式**: 设置 `private_key_path`
6. **平台证书配置**: 用于验签微信支付返回数据（可选但推荐）
   - **文件路径方式**: 设置 `platform_cert_path` 和 `platform_cert_serial_no`
   - **内容方式**: 设置 `platform_cert` 和 `platform_cert_serial_no`
7. **is_prod**:
   - `false`: 使用沙箱环境
   - `true`: 使用生产环境

#### 获取微信支付配置步骤

1. 登录 [微信支付商户平台](https://pay.weixin.qq.com/)
2. 获取商户号 `mch_id`
3. 设置API密钥 `api_key`
4. 下载API证书文件
5. 获取证书序列号和APIv3密钥

## 环境配置

### 开发环境配置

```yaml
payment:
  alipay:
    app_id: "2021000000000000"  # 沙箱应用ID
    is_prod: false
  wechat:
    app_id: "wx_sandbox_app_id"
    mch_id: "sandbox_mch_id"
    is_prod: false
```

### 生产环境配置

```yaml
payment:
  alipay:
    app_id: "2019000000000000"  # 正式应用ID
    is_prod: true
  wechat:
    app_id: "wx_production_app_id"
    mch_id: "production_mch_id"
    is_prod: true
```

## 证书管理

### 证书文件方式（推荐）

适用于服务器部署，证书文件存储在安全目录：

```yaml
wechat:
  cert_path: "/etc/pay-core/certs/apiclient_cert.pem"
  key_path: "/etc/pay-core/certs/apiclient_key.pem"
```

### 证书内容方式

适用于容器化部署，通过环境变量或配置管理系统注入：

```yaml
wechat:
  cert_data: |
    -----BEGIN CERTIFICATE-----
    MIIEaTCCA1GgAwIBAgIUYm4...
    -----END CERTIFICATE-----
  key_data: |
    -----BEGIN PRIVATE KEY-----
    MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
    -----END PRIVATE KEY-----
```

## 配置验证

系统启动时会验证配置的完整性：

1. **必填字段检查**: 验证必要的配置项是否填写
2. **格式验证**: 检查密钥、证书格式是否正确
3. **连通性测试**: 测试与支付平台的连接

## 安全建议

1. **密钥安全**:
   - 不要将密钥提交到版本控制系统
   - 使用环境变量或密钥管理系统
   - 定期轮换密钥

2. **证书管理**:
   - 证书文件权限设置为 600
   - 定期检查证书有效期
   - 及时更新过期证书

3. **配置文件**:
   - 配置文件权限设置为 640
   - 使用配置加密工具
   - 区分不同环境的配置

## 常见问题

### Q: 支付宝公钥和私钥的区别？
A: 
- 私钥：应用私钥，用于签名请求，需要保密
- 公钥：支付宝公钥，用于验证支付宝返回数据，可以公开

### Q: 微信支付证书何时需要？
A: 
- 退款接口必须使用证书
- 部分敏感接口需要证书
- 建议生产环境都配置证书

### Q: 如何测试配置是否正确？
A: 
1. 启动系统查看日志
2. 调用创建订单接口
3. 检查返回的二维码是否正常

### Q: 沙箱环境和生产环境的区别？
A:
- 沙箱环境：用于测试，不产生真实交易
- 生产环境：真实交易环境，需要真实的商户资质

## 配置模板

参考 `configs/config.example.yaml` 文件获取完整的配置模板。
