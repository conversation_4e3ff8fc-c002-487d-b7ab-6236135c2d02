import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

public class APIKeyGenerator {
    
    private static final String HMAC_SHA256 = "HmacSHA256";
    
    /**
     * 生成API Key
     * @param deviceSN 设备序列号
     * @param sharedKey 共享密钥
     * @return Base64编码的API Key
     */
    public static String generateAPIKey(String deviceSN, String sharedKey) {
        if (deviceSN == null || deviceSN.isEmpty()) {
            throw new IllegalArgumentException("Device SN cannot be empty");
        }
        if (sharedKey == null || sharedKey.isEmpty()) {
            throw new IllegalArgumentException("Shared key cannot be empty");
        }
        
        try {
            // 1. 使用设备SN作为payload
            String payload = deviceSN;
            
            // 2. 创建HMAC-SHA256实例
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKeySpec = new SecretKeySpec(
                sharedKey.getBytes(StandardCharsets.UTF_8), 
                HMAC_SHA256
            );
            mac.init(secretKeySpec);
            
            // 3. 计算签名
            byte[] signatureBytes = mac.doFinal(payload.getBytes(StandardCharsets.UTF_8));
            String signature = bytesToHex(signatureBytes);
            
            // 4. 组合最终数据并Base64编码（使用标准URL编码，与Go保持一致）
            String finalData = payload + ":" + signature;
            return Base64.getUrlEncoder().encodeToString(finalData.getBytes(StandardCharsets.UTF_8));
            
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("Failed to generate API key: " + e.getMessage(), e);
        }
    }
    
    /**
     * 字节数组转十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
    
    // 使用示例
    public static void main(String[] args) {
        String deviceSN = "100000064787";
        String sharedKey = "default-shared-key-change-in-production";
        
        try {
            String apiKey = generateAPIKey(deviceSN, sharedKey);
            System.out.println("Generated API Key: " + apiKey);
            
            // 验证生成的API Key
            System.out.println("API Key长度: " + apiKey.length());
            
            // 解码验证
            byte[] decoded = Base64.getUrlDecoder().decode(apiKey);
            String decodedStr = new String(decoded, StandardCharsets.UTF_8);
            System.out.println("解码后: " + decodedStr);
            
            String[] parts = decodedStr.split(":");
            System.out.println("设备SN: " + parts[0]);
            System.out.println("签名长度: " + parts[1].length());
            System.out.println("签名: " + parts[1]);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}