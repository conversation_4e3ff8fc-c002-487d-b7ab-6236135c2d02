# 微信支付查询订单状态失败问题排查指南

## 问题现象

```
{"channel":"wechat","duration":268,"error":"wechat V3 query order failed: ","level":"error","msg":"Failed to query payment order status","order_no":"20250823225357925ba5","time":"2025-08-23 22:56:40","transaction_id":""}
```

## 根本原因

通过调试发现，问题出现在微信支付客户端初始化时的自动验签设置失败：

```
Failed to setup auto verify: GetAndSelectNewestCert() failed or certs is empty: &{Code:0 Certs:[] ErrResponse:{Code: Message: Detail:{Field: Value: Issue: Location:}} Error:}
```

## 可能的原因

### 1. 网络连接问题
- 无法访问微信支付API服务器
- 防火墙或代理设置阻止了HTTPS请求

### 2. 证书配置问题
- 商户证书序列号错误
- 私钥文件损坏或格式不正确
- APIv3Key配置错误

### 3. 商户号配置问题
- 商户号未激活或配置错误
- 商户号与证书不匹配

## 解决方案

### 方案1：使用本地平台证书（推荐）

修改配置文件，使用本地平台证书而不是自动获取：

```yaml
payment:
  wechat:
    app_id: "wxcf9a42f63c23a637"
    mch_id: "1264055901"
    is_prod: false
    apiv3_key: "ZxQr5VpJ9sW4nGfYhLdK8cT3bRm7eNg2"
    serial_no: "7042C24F0C04F7DEC91393E505163FCA3170B46B"
    private_key_path: "configs/1264055901_20250822_cert/apiclient_key.pem"
    
    # 使用本地平台证书
    platform_cert_path: "configs/wechat_platform_cert.pem"  # 下载的微信平台证书
    platform_cert_serial_no: "微信平台证书序列号"  # 从微信商户平台获取
```

### 方案2：修复自动验签

如果要继续使用自动验签，需要确保：

1. **检查网络连接**
```bash
curl -v https://api.mch.weixin.qq.com/v3/certificates
```

2. **验证证书配置**
```bash
openssl x509 -in configs/1264055901_20250822_cert/apiclient_cert.pem -noout -serial
```

3. **验证私钥格式**
```bash
openssl rsa -in configs/1264055901_20250822_cert/apiclient_key.pem -check
```

### 方案3：临时跳过验签（仅用于调试）

```go
// 在客户端初始化后，暂时跳过验签设置
client, err := wechatv3.NewClientV3(mchID, serialNo, apiV3Key, privateKey)
if err != nil {
    return nil, err
}

// 暂时不设置验签，仅用于调试
// err = client.AutoVerifySign()
```

## 获取微信平台证书的步骤

1. **登录微信商户平台**
   - 访问：https://pay.weixin.qq.com/

2. **下载平台证书**
   - 进入：账户中心 -> API安全 -> 平台证书
   - 下载平台证书文件

3. **获取证书序列号**
   - 在API安全页面查看平台证书序列号

4. **更新配置文件**
   - 将证书文件放到configs目录
   - 更新platform_cert_path和platform_cert_serial_no配置

## 验证修复

运行以下测试程序验证修复：

```bash
go run test/debug_wechat_simple.go
```

成功的输出应该包含：
```
微信支付客户端创建成功
Auto verify setup successful
开始查询订单: 20250823225357925ba5
```

## 预防措施

1. **定期更新平台证书**
   - 微信平台证书有有效期，需要定期更新

2. **监控证书状态**
   - 在应用启动时检查证书有效性

3. **备用方案**
   - 配置本地证书作为自动获取的备用方案

4. **日志监控**
   - 监控微信支付相关的错误日志
   - 设置告警机制
