# Pay Core API 文档

本目录包含了支付系统核心API的完整文档。

## 文档列表

### API规范文档
- **[openapi.yaml](./openapi.yaml)** - OpenAPI 3.0规范文件，包含完整的API定义
- **[openapi-guide.md](./openapi-guide.md)** - OpenAPI文档使用指南
- **[api-examples.md](./api-examples.md)** - API使用示例和认证说明

### 系统设计文档
- **[01-system-architecture.md](./01-system-architecture.md)** - 系统架构设计
- **[02-database-design.md](./02-database-design.md)** - 数据库设计
- **[03-technology-stack.md](./03-technology-stack.md)** - 技术栈说明
- **[04-implementation-plan.md](./04-implementation-plan.md)** - 实施计划
- **[05-project-summary.md](./05-project-summary.md)** - 项目总结
- **[06-deployment-guide.md](./06-deployment-guide.md)** - 部署指南

### 功能特性文档
- **[auto-reconcile-feature.md](./auto-reconcile-feature.md)** - 自动对账功能
- **[scheduler-service.md](./scheduler-service.md)** - 定时任务服务
- **[wechat_v3.md](./wechat_v3.md)** - 微信支付V3接口
- **[configuration.md](./configuration.md)** - 配置说明

## 快速开始

### 1. 查看API文档

#### 在线查看
使用Swagger UI在线查看API文档：

```bash
# 使用Docker运行Swagger UI
docker run -p 8081:8080 \
  -e SWAGGER_JSON=/openapi.yaml \
  -v $(pwd)/docs/openapi.yaml:/openapi.yaml \
  swaggerapi/swagger-ui
```

然后访问 `http://localhost:8081` 查看API文档。

#### 使用Swagger Editor
1. 访问 [Swagger Editor](https://editor.swagger.io/)
2. 导入 `docs/openapi.yaml` 文件

### 2. 验证API文档

运行验证脚本检查OpenAPI文档的有效性：

```bash
./scripts/validate-openapi.sh
```

### 3. 生成客户端SDK

使用OpenAPI Generator生成各种语言的客户端SDK：

```bash
# 安装OpenAPI Generator
npm install @openapitools/openapi-generator-cli -g

# 生成Java客户端
openapi-generator-cli generate \
  -i docs/openapi.yaml \
  -g java \
  -o ./generated/java-client

# 生成Python客户端
openapi-generator-cli generate \
  -i docs/openapi.yaml \
  -g python \
  -o ./generated/python-client

# 生成Go客户端
openapi-generator-cli generate \
  -i docs/openapi.yaml \
  -g go \
  -o ./generated/go-client
```

## API概览

### 核心功能

1. **支付管理**
   - 创建支付订单
   - 查询订单状态
   - 订单详情获取

2. **退款管理**
   - 申请退款
   - 退款状态查询

3. **对账管理**
   - 启动对账任务
   - 对账结果查询
   - 对账明细查看

4. **回调通知**
   - 支付结果回调
   - 退款结果回调

### 支持的支付渠道

- **支付宝** (alipay)
- **微信支付** (wechat)
- **QQ钱包** (qq)

### 支持的支付方式

- **扫码支付** (native)
- **H5支付** (h5)
- **APP支付** (app)
- **小程序支付** (mini)

## 认证方式

本系统使用HMAC-SHA256签名认证，需要在请求头中包含：

- `X-API-Key`: 客户端ID，格式为 `{appCode}_{appUserId}`
- `X-Timestamp`: 时间戳
- `X-Nonce`: 随机数（防重放）
- `X-Signature`: 签名值

详细的认证说明请参考 [api-examples.md](./api-examples.md)。

## 开发工具

### 推荐的开发工具

1. **Swagger UI** - API文档查看和测试
2. **Postman** - API测试工具
3. **OpenAPI Generator** - 客户端SDK生成
4. **Insomnia** - API测试工具

### 客户端SDK

项目提供了多种语言的客户端SDK示例：

- **Go** - `examples/client_sdk.go`
- **Java** - `examples/PayCoreHttpTest.java`

## 部署说明

### 环境要求

- Go 1.19+
- MySQL 8.0+
- Redis 6.0+

### 配置文件

主要配置文件位于 `configs/` 目录：

- `config.yaml` - 主配置文件
- `config.dev.yaml` - 开发环境配置
- `config.example.yaml` - 配置模板

### 启动服务

```bash
# 编译
make build

# 运行
./server
```

详细的部署说明请参考 [06-deployment-guide.md](./06-deployment-guide.md)。

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目采用 Apache 2.0 许可证。详情请参考 LICENSE 文件。

## 联系我们

如有问题或建议，请通过以下方式联系：

- 创建 Issue
- 发送邮件至 <EMAIL>
- 查看更多文档：https://github.com/your-org/pay-core
