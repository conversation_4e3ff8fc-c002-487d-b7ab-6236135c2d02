# 支付系统架构设计

## 1. 系统概述

基于 gopay 构建的支付系统，主要提供支付订单管理、对账和 Native 支付功能。系统采用微服务架构，确保高可用性、可扩展性和安全性。

## 2. 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │  Mobile App     │    │  Third Party    │
│                 │    │                 │    │  Merchants      │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      API Gateway          │
                    │   (Authentication &       │
                    │    Rate Limiting)         │
                    └─────────────┬─────────────┘
                                  │
          ┌───────────────────────┼───────────────────────┐
          │                       │                       │
┌─────────┴─────────┐   ┌─────────┴─────────┐   ┌─────────┴─────────┐
│  Payment Service  │   │  Order Service    │   │ Reconcile Service │
│                   │   │                   │   │                   │
│ - Native Payment  │   │ - Order Creation  │   │ - Bill Download   │
│ - Payment Query   │   │ - Order Query     │   │ - Reconciliation  │
│ - Refund          │   │ - Order Update    │   │ - Report Generate │
│ - Notification    │   │ - Order History   │   │                   │
└─────────┬─────────┘   └─────────┬─────────┘   └─────────┬─────────┘
          │                       │                       │
          └───────────────────────┼───────────────────────┘
                                  │
                    ┌─────────────┴─────────────┐
                    │     Message Queue         │
                    │    (Redis/RabbitMQ)       │
                    └─────────────┬─────────────┘
                                  │
          ┌───────────────────────┼───────────────────────┐
          │                       │                       │
┌─────────┴─────────┐   ┌─────────┴─────────┐   ┌─────────┴─────────┐
│   MySQL Master    │   │   MySQL Slave     │   │      Redis        │
│                   │   │                   │   │                   │
│ - Orders          │   │ - Read Replicas   │   │ - Cache           │
│ - Payments        │   │ - Analytics       │   │ - Session         │
│ - Users           │   │                   │   │ - Rate Limiting   │
│ - Reconciliation  │   │                   │   │                   │
└───────────────────┘   └───────────────────┘   └───────────────────┘
```

## 3. 核心服务模块

### 3.1 API Gateway
- **功能**: 统一入口，负责路由、认证、限流、监控
- **技术**: Gin + JWT + Redis
- **职责**:
  - 请求路由和负载均衡
  - API 认证和授权
  - 请求限流和熔断
  - 日志记录和监控

### 3.2 Payment Service (支付服务)
- **功能**: 核心支付逻辑，基于 gopay 实现
- **支持支付方式**: Native 支付（扫码支付）
- **主要功能**:
  - 创建支付订单
  - 处理支付回调
  - 查询支付状态
  - 处理退款请求
  - 签名验证

### 3.3 Order Service (订单服务)
- **功能**: 支付订单生命周期管理
- **主要功能**:
  - 订单创建和验证
  - 订单状态管理
  - 订单查询和历史记录
  - 订单统计和报表

### 3.4 Reconcile Service (对账服务)
- **功能**: 支付对账和财务管理
- **主要功能**:
  - 下载支付平台账单
  - 自动对账处理
  - 差异处理和报告
  - 财务报表生成

## 4. 数据流向

### 4.1 支付流程
```
1. 用户发起支付请求 → API Gateway
2. API Gateway → Payment Service
3. Payment Service → gopay → 支付平台
4. 支付平台返回支付二维码
5. 用户扫码支付
6. 支付平台异步通知 → Payment Service
7. Payment Service → Order Service (更新订单状态)
8. Payment Service → Message Queue (发送通知)
```

### 4.2 对账流程
```
1. 定时任务触发 → Reconcile Service
2. Reconcile Service → 支付平台 (下载账单)
3. Reconcile Service → 数据库 (获取本地订单)
4. 执行对账逻辑，生成对账报告
5. 异常订单处理和通知
```

## 5. 技术架构特点

### 5.1 高可用性
- 服务无状态设计
- 数据库主从复制
- Redis 集群部署
- 服务熔断和降级

### 5.2 安全性
- JWT 认证机制
- API 签名验证
- 敏感数据加密
- 请求限流防护

### 5.3 可扩展性
- 微服务架构
- 水平扩展支持
- 消息队列解耦
- 缓存层优化

### 5.4 监控和日志
- 结构化日志记录
- 性能监控指标
- 错误追踪和告警
- 业务数据统计

## 6. 部署架构

### 6.1 容器化部署
- Docker 容器化
- Kubernetes 编排
- 服务发现和配置管理
- 自动扩缩容

### 6.2 环境划分
- 开发环境 (Development)
- 测试环境 (Testing)
- 预生产环境 (Staging)
- 生产环境 (Production)

## 7. 安全考虑

### 7.1 数据安全
- 敏感数据加密存储
- 传输层 TLS 加密
- 数据库访问控制
- 定期安全审计

### 7.2 接口安全
- API 签名验证
- 请求频率限制
- IP 白名单机制
- 异常行为监控

### 7.3 业务安全
- 订单幂等性保证
- 重复支付防护
- 金额校验机制
- 异常交易监控
