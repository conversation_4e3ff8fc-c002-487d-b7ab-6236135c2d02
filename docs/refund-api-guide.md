# 退款API使用指南

## 概述

本文档介绍PayCore系统的退款功能，包括退款申请和退款回调处理。

## 功能特性

### ✅ 已实现功能

1. **退款申请接口** - 支持微信支付V3 API退款申请
2. **退款回调处理** - 支持微信支付退款结果通知
3. **退款状态管理** - 完整的退款订单状态跟踪
4. **退款记录管理** - 详细的退款交易记录
5. **数据一致性** - 原订单和退款订单状态同步更新

### 🔄 支持的退款类型

- **部分退款** - 可以退款订单的部分金额
- **全额退款** - 可以退款订单的全部金额
- **多次退款** - 支持同一订单多次部分退款（需要微信支付支持）

## API接口

### 1. 申请退款

**接口地址：** `POST /api/v1/orders/{order_no}/refund`

**请求头：**
```
Content-Type: application/json
X-API-Key: {client_id}
X-Timestamp: {timestamp}
X-Nonce: {nonce}
X-Signature: {signature}
```

**请求参数：**
```json
{
  "app_refund_no": "REFUND_123456",
  "refund_amount": 50.25,
  "refund_reason": "用户申请退款"
}
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "refund_no": "REF20240101001",
    "order_no": "PAY20240101001",
    "app_id": 1,
    "app_refund_no": "REFUND_123456",
    "refund_amount": 50.25,
    "refund_reason": "用户申请退款",
    "status": "PROCESSING",
    "out_refund_no": "wx_refund_123456",
    "success_time": null,
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

### 2. 退款回调通知

**接口地址：** `POST /notify/{channel}/refund`

微信支付会向此接口发送退款结果通知。

**回调处理流程：**
1. 验证回调签名
2. 解密回调数据
3. 更新退款订单状态
4. 更新原订单状态
5. 创建退款记录
6. 返回处理结果

## 数据模型

### 退款订单 (RefundOrder)

```go
type RefundOrder struct {
    ID            uint64          `json:"id"`
    RefundNo      string          `json:"refund_no"`      // 退款单号
    OrderNo       string          `json:"order_no"`       // 原支付订单号
    AppID         uint64          `json:"app_id"`         // 应用ID
    AppRefundNo   string          `json:"app_refund_no"`  // 业务方退款单号
    RefundAmount  decimal.Decimal `json:"refund_amount"`  // 退款金额
    RefundReason  string          `json:"refund_reason"`  // 退款原因
    Status        string          `json:"status"`         // 退款状态
    OutRefundNo   string          `json:"out_refund_no"`  // 第三方退款单号
    SuccessTime   *types.Time     `json:"success_time"`   // 退款成功时间
    CreatedAt     types.Time      `json:"created_at"`
    UpdatedAt     types.Time      `json:"updated_at"`
}
```

### 退款记录 (RefundRecord)

```go
type RefundRecord struct {
    ID                 uint64          `json:"id"`
    RefundNo           string          `json:"refund_no"`           // 退款单号
    OutRefundNo        string          `json:"out_refund_no"`       // 第三方退款单号
    RefundTradeNo      string          `json:"refund_trade_no"`     // 第三方退款交易号
    PayChannel         string          `json:"pay_channel"`         // 支付渠道
    OrderNo            string          `json:"order_no"`            // 原支付订单号
    OutTradeNo         string          `json:"out_trade_no"`        // 原第三方支付订单号
    RefundAmount       decimal.Decimal `json:"refund_amount"`       // 申请退款金额
    ActualRefundAmount decimal.Decimal `json:"actual_refund_amount"` // 实际退款金额
    RefundFee          decimal.Decimal `json:"refund_fee"`          // 退款手续费
    RefundTime         types.Time      `json:"refund_time"`         // 退款时间
    ReconcileStatus    string          `json:"reconcile_status"`    // 对账状态
    SettlementStatus   string          `json:"settlement_status"`   // 结算状态
    CreatedAt          types.Time      `json:"created_at"`
    UpdatedAt          types.Time      `json:"updated_at"`
}
```

## 状态说明

### 退款状态 (RefundStatus)

- `PROCESSING` - 处理中（退款申请已提交，等待支付平台处理）
- `SUCCESS` - 成功（退款已完成）
- `FAILED` - 失败（退款申请被拒绝或处理失败）

### 订单状态变化

当退款成功时，原订单状态会从 `PAID` 更新为 `REFUNDED`。

## 使用示例

### 1. 申请退款

```bash
curl -X POST "http://localhost:8080/api/v1/orders/PAY20240101001/refund" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: robot_user123" \
  -H "X-Timestamp: 1640995200" \
  -H "X-Nonce: abc123" \
  -H "X-Signature: signature_value" \
  -d '{
    "app_refund_no": "REFUND_123456",
    "refund_amount": 50.25,
    "refund_reason": "用户申请退款"
  }'
```

### 2. 查询退款状态

目前可以通过查询原订单状态来了解退款情况：

```bash
curl -X GET "http://localhost:8080/api/v1/orders/PAY20240101001" \
  -H "X-API-Key: robot_user123" \
  -H "X-Timestamp: 1640995200" \
  -H "X-Nonce: abc123" \
  -H "X-Signature: signature_value"
```

## 错误处理

### 常见错误码

- `400` - 请求参数错误
- `401` - 认证失败
- `404` - 订单不存在
- `500` - 服务器内部错误

### 业务错误

- `order not found` - 订单不存在
- `access denied` - 无权限访问该订单
- `order cannot be refunded` - 订单状态不允许退款
- `refund amount exceeds order amount` - 退款金额超过订单金额

## 数据库表记录情况

### ✅ 完整实现的表记录功能

1. **退款订单表 (refund_orders)** - ✅ 完全实现
   - 在 `RefundOrder()` 方法中创建退款订单记录
   - 在 `handleRefundSuccess()` 中更新退款订单状态

2. **退款记录表 (refund_records)** - ✅ 完全实现
   - 在 `handleRefundSuccess()` 中创建退款记录

3. **支付订单表 (payment_orders)** - ✅ 完全实现
   - 在 `CreateOrder()` 中创建支付订单
   - 在 `handlePaymentSuccess()` 中更新支付状态

4. **支付记录表 (payment_records)** - ✅ 完全实现
   - 在 `handlePaymentSuccess()` 中创建支付记录

5. **回调日志表 (notify_logs)** - ✅ 新增完整实现
   - 在 `HandleNotify()` 中记录所有回调日志
   - 支持支付回调和退款回调的完整记录
   - 包含请求体、响应状态、错误信息等详细信息

### 📊 回调日志记录详情

- **支付成功回调** - 记录类型：`PAYMENT`，来源：`PLATFORM`
- **退款成功回调** - 记录类型：`REFUND`，来源：`PLATFORM`
- **解析失败回调** - 记录类型：`UNKNOWN`，状态：`FAILED`
- **处理失败回调** - 记录状态：`FAILED`，包含错误信息

## 注意事项

1. **退款条件**：只有状态为 `PAID` 的订单才能申请退款
2. **金额限制**：退款金额不能超过原订单金额
3. **幂等性**：相同的 `app_refund_no` 只能提交一次
4. **回调处理**：退款回调可能会重复发送，系统会自动去重
5. **异步处理**：退款申请提交后，实际退款结果通过回调通知
6. **日志记录**：所有回调都会记录到 `notify_logs` 表中，便于问题排查

## 配置说明

### 微信支付配置

确保在 `config.yaml` 中正确配置微信支付参数：

```yaml
payment:
  wechat:
    app_id: "your_app_id"
    mch_id: "your_merchant_id"
    apiv3_key: "your_apiv3_key"
    serial_no: "your_cert_serial_no"
    private_key: "your_private_key_content"
```

### 回调URL配置

退款回调URL格式：`{base_url}/notify/{channel}/refund`

例如：`https://your-domain.com/notify/wechat/refund`

## 测试

可以使用 `test/test_refund_api.go` 文件进行退款功能测试：

```bash
go run test/test_refund_api.go
```

注意：测试需要真实的微信支付配置，或者可以修改测试代码使用模拟数据。
