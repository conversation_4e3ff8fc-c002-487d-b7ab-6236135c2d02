# Pay Core API OpenAPI 文档总结

## 📋 文档概览

本项目已成功生成完整的OpenAPI 3.0规范文档，涵盖了支付系统的所有核心功能。

### 📊 统计信息

- **OpenAPI版本**: 3.0.3
- **API标题**: Pay Core API
- **API版本**: 1.0.0
- **API路径数量**: 9个
- **API方法数量**: 9个
- **数据模型数量**: 9个
- **API标签数量**: 4个
- **服务器配置**: 2个（开发环境和生产环境）
- **安全方案**: 1个（HMAC-SHA256签名认证）

## 🔗 API接口列表

### 1. 系统监控
| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/health` | 健康检查 |

### 2. 支付管理
| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/v1/orders` | 创建支付订单 |
| GET | `/api/v1/orders/{order_no}` | 获取订单信息 |
| GET | `/api/v1/orders/query` | 查询订单状态 |
| POST | `/api/v1/orders/{order_no}/refund` | 申请退款 |

### 3. 支付回调
| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/notify/{channel}` | 支付回调通知 |

### 4. 对账管理
| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/v1/reconciliation/start` | 开始对账 |
| GET | `/api/v1/reconciliation/batches/{batchNo}` | 获取对账批次详情 |
| GET | `/api/v1/reconciliation/details` | 获取对账明细列表 |

## 📦 数据模型

### 核心实体模型
1. **PaymentOrder** - 支付订单实体
2. **RefundOrder** - 退款订单实体
3. **ReconciliationBatch** - 对账批次实体
4. **ReconciliationDetail** - 对账明细实体

### 请求模型
1. **CreateOrderRequest** - 创建订单请求
2. **RefundOrderRequest** - 退款请求
3. **StartReconciliationRequest** - 开始对账请求

### 响应模型
1. **ApiResponse** - 通用API响应
2. **ErrorResponse** - 错误响应

## 🔐 认证方式

采用HMAC-SHA256签名认证，包含以下请求头：

- **X-API-Key**: 客户端ID，格式为 `{appCode}_{appUserId}`
- **X-Timestamp**: 时间戳
- **X-Nonce**: 随机数（防重放）
- **X-Signature**: 签名值

## 🏷️ API标签分类

1. **系统监控** - 系统状态监控相关接口
2. **支付管理** - 支付订单管理相关接口
3. **支付回调** - 支付平台回调通知接口
4. **对账管理** - 对账相关接口

## 🌐 服务器配置

1. **开发环境**: `http://localhost:8080`
2. **生产环境**: `https://api.paycore.com`

## 📁 文件结构

```
docs/
├── openapi.yaml              # OpenAPI 3.0规范文件
├── openapi-guide.md          # OpenAPI使用指南
├── openapi-summary.md        # 本文档
├── README.md                 # 文档总览
└── api-examples.md           # API使用示例

scripts/
├── validate-openapi.sh       # OpenAPI验证脚本
└── validate_yaml.py          # Python YAML验证脚本
```

## 🚀 使用方法

### 1. 查看API文档

#### 在线查看（推荐）
访问 [Swagger Editor](https://editor.swagger.io/) 并导入 `docs/openapi.yaml` 文件。

#### 本地查看
```bash
docker run -p 8081:8080 \
  -e SWAGGER_JSON=/openapi.yaml \
  -v $(pwd)/docs/openapi.yaml:/openapi.yaml \
  swaggerapi/swagger-ui
```

### 2. 验证文档有效性

```bash
# 使用Shell脚本验证
./scripts/validate-openapi.sh

# 使用Python脚本验证
python3 scripts/validate_yaml.py
```

### 3. 生成客户端SDK

```bash
# 安装OpenAPI Generator
npm install @openapitools/openapi-generator-cli -g

# 生成各种语言的客户端
openapi-generator-cli generate -i docs/openapi.yaml -g java -o ./generated/java-client
openapi-generator-cli generate -i docs/openapi.yaml -g python -o ./generated/python-client
openapi-generator-cli generate -i docs/openapi.yaml -g go -o ./generated/go-client
openapi-generator-cli generate -i docs/openapi.yaml -g javascript -o ./generated/js-client
```

## ✅ 验证结果

✅ **YAML语法检查通过**  
✅ **OpenAPI规范验证通过**  
✅ **数据模型定义完整**  
✅ **API路径覆盖全面**  
✅ **认证方式配置正确**  
✅ **响应格式统一规范**  

## 🎯 功能特性

### 支付功能
- ✅ 支持多种支付渠道（支付宝、微信支付、QQ钱包）
- ✅ 支持多种支付方式（扫码、H5、APP、小程序）
- ✅ 订单状态实时查询
- ✅ 支付结果异步回调

### 退款功能
- ✅ 部分退款和全额退款
- ✅ 退款状态跟踪
- ✅ 退款结果通知

### 对账功能
- ✅ 自动对账任务
- ✅ 对账结果分析
- ✅ 差异处理机制

### 安全特性
- ✅ HMAC-SHA256签名认证
- ✅ 时间戳防重放攻击
- ✅ 随机数防重复请求
- ✅ 应用级别权限控制

## 📚 相关文档

- [API使用示例](./api-examples.md) - 详细的API调用示例
- [OpenAPI使用指南](./openapi-guide.md) - OpenAPI工具使用说明
- [系统架构设计](./01-system-architecture.md) - 系统整体架构
- [数据库设计](./02-database-design.md) - 数据库表结构设计

## 🔄 更新日志

### v1.0.0 (当前版本)
- ✅ 完成OpenAPI 3.0规范文档
- ✅ 包含所有核心API接口
- ✅ 完整的数据模型定义
- ✅ 统一的错误处理机制
- ✅ 详细的认证说明
- ✅ 完善的示例和文档

## 📞 技术支持

如需技术支持或有任何问题，请：

1. 查看相关文档
2. 创建GitHub Issue
3. 联系技术支持团队

---

**文档生成时间**: 2024年1月  
**文档版本**: v1.0.0  
**OpenAPI版本**: 3.0.3
