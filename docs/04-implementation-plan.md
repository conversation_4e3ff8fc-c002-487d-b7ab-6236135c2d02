# 支付系统实现计划

## 1. 项目概述

基于 gopay 构建的支付系统，采用微服务架构，主要实现支付订单管理、对账和 Native 支付功能。

## 2. 开发阶段规划

### 阶段一：项目基础搭建 (1-2天)
- [x] 分析 gopay 项目架构
- [x] 系统架构设计
- [x] 数据库设计
- [x] 技术栈选择
- [ ] 项目结构初始化
- [ ] 基础配置和依赖管理
- [ ] 数据库连接和迁移
- [ ] 基础中间件开发

### 阶段二：核心支付模块 (3-4天)
- [ ] gopay 集成和封装
- [ ] Native 支付接口实现
- [ ] 支付回调处理
- [ ] 签名验证机制
- [ ] 支付状态查询
- [ ] 退款功能实现

### 阶段三：订单管理模块 (2-3天)
- [ ] 订单创建和验证
- [ ] 订单状态管理
- [ ] 订单查询接口
- [ ] 订单历史记录
- [ ] 订单统计功能

### 阶段四：对账模块 (2-3天)
- [ ] 账单下载功能
- [ ] 对账逻辑实现
- [ ] 差异处理机制
- [ ] 对账报告生成
- [ ] 定时任务调度

### 阶段五：API接口和文档 (1-2天)
- [ ] RESTful API 设计
- [ ] 接口参数验证
- [ ] 错误处理机制
- [ ] API 文档生成
- [ ] 接口测试用例

### 阶段六：测试和优化 (2-3天)
- [ ] 单元测试编写
- [ ] 集成测试
- [ ] 性能测试
- [ ] 安全测试
- [ ] 代码优化

## 3. 详细实现步骤

### 3.1 项目结构初始化

```
pay-core/
├── cmd/                    # 应用入口
│   └── server/
│       └── main.go
├── internal/               # 内部包
│   ├── config/            # 配置管理
│   ├── handler/           # HTTP处理器
│   ├── service/           # 业务逻辑
│   ├── repository/        # 数据访问
│   ├── model/             # 数据模型
│   ├── middleware/        # 中间件
│   └── pkg/               # 内部工具包
├── pkg/                   # 公共包
│   ├── payment/           # 支付相关
│   ├── database/          # 数据库
│   ├── cache/             # 缓存
│   ├── logger/            # 日志
│   └── utils/             # 工具函数
├── api/                   # API定义
│   └── v1/
├── docs/                  # 文档
├── scripts/               # 脚本
├── deployments/           # 部署配置
├── test/                  # 测试
├── go.mod
├── go.sum
├── Dockerfile
├── docker-compose.yml
└── README.md
```

### 3.2 核心模块实现顺序

#### 3.2.1 配置管理模块
```go
// internal/config/config.go
type Config struct {
    Server   ServerConfig   `mapstructure:"server"`
    Database DatabaseConfig `mapstructure:"database"`
    Redis    RedisConfig    `mapstructure:"redis"`
    Payment  PaymentConfig  `mapstructure:"payment"`
    Log      LogConfig      `mapstructure:"log"`
}
```

#### 3.2.2 数据库模型
```go
// internal/model/payment_order.go
type PaymentOrder struct {
    ID               uint64    `gorm:"primaryKey"`
    OrderNo          string    `gorm:"uniqueIndex"`
    MerchantID       uint64    `gorm:"index"`
    MerchantOrderNo  string    
    Amount           decimal.Decimal
    Status           int8      `gorm:"index"`
    PaymentMethod    string
    PaymentChannel   string
    // ... 其他字段
}
```

#### 3.2.3 支付服务接口
```go
// internal/service/payment.go
type PaymentService interface {
    CreateOrder(ctx context.Context, req *CreateOrderRequest) (*PaymentOrder, error)
    QueryOrder(ctx context.Context, orderNo string) (*PaymentOrder, error)
    HandleNotify(ctx context.Context, channel string, data []byte) error
    RefundOrder(ctx context.Context, req *RefundRequest) (*RefundOrder, error)
}
```

#### 3.2.4 HTTP处理器
```go
// internal/handler/payment.go
type PaymentHandler struct {
    paymentService service.PaymentService
}

func (h *PaymentHandler) CreateOrder(c *gin.Context) {
    // 实现创建订单接口
}
```

### 3.3 关键技术实现

#### 3.3.1 gopay 集成
```go
// pkg/payment/client.go
type Client struct {
    alipayClient *alipay.Client
    wechatClient *wechat.Client
}

func (c *Client) CreateNativeOrder(req *NativeOrderRequest) (*NativeOrderResponse, error) {
    switch req.Channel {
    case "alipay":
        return c.createAlipayNativeOrder(req)
    case "wechat":
        return c.createWechatNativeOrder(req)
    default:
        return nil, errors.New("unsupported payment channel")
    }
}
```

#### 3.3.2 签名验证
```go
// pkg/payment/verify.go
func VerifyNotifySign(channel string, data []byte, sign string) (bool, error) {
    switch channel {
    case "alipay":
        return alipay.VerifySign(publicKey, data, sign)
    case "wechat":
        return wechat.VerifySign(apiKey, signType, data)
    default:
        return false, errors.New("unsupported channel")
    }
}
```

#### 3.3.3 对账逻辑
```go
// internal/service/reconcile.go
func (s *ReconcileService) Reconcile(ctx context.Context, date time.Time, channel string) error {
    // 1. 下载渠道账单
    bill, err := s.downloadBill(ctx, date, channel)
    if err != nil {
        return err
    }
    
    // 2. 获取本地订单
    orders, err := s.getLocalOrders(ctx, date, channel)
    if err != nil {
        return err
    }
    
    // 3. 执行对账
    diffs := s.compareOrders(bill, orders)
    
    // 4. 保存对账结果
    return s.saveReconcileResult(ctx, date, channel, diffs)
}
```

## 4. 开发规范

### 4.1 代码规范
- 遵循 Go 官方代码规范
- 使用 golangci-lint 进行代码检查
- 函数和方法添加注释
- 错误处理要完整

### 4.2 Git 规范
- 使用 Git Flow 工作流
- Commit 信息格式：`type(scope): description`
- 功能分支开发，合并前 Code Review

### 4.3 测试规范
- 单元测试覆盖率 > 80%
- 集成测试覆盖主要业务流程
- 使用 testify 进行断言
- Mock 外部依赖

### 4.4 文档规范
- API 文档使用 Swagger 生成
- 代码注释要清晰
- README 包含部署说明
- 变更记录维护 CHANGELOG

## 5. 部署方案

### 5.1 开发环境
- Docker Compose 一键启动
- 热重载支持
- 本地数据库和缓存

### 5.2 测试环境
- Kubernetes 部署
- 自动化测试流水线
- 测试数据管理

### 5.3 生产环境
- 高可用部署
- 监控告警
- 日志收集
- 备份策略

## 6. 风险控制

### 6.1 技术风险
- 第三方支付接口变更
- 数据库性能瓶颈
- 缓存雪崩问题
- 网络超时处理

### 6.2 业务风险
- 重复支付防护
- 订单状态一致性
- 对账差异处理
- 资金安全保障

### 6.3 运维风险
- 服务可用性监控
- 数据备份恢复
- 安全漏洞防护
- 容量规划

## 7. 质量保证

### 7.1 代码质量
- 代码审查机制
- 自动化测试
- 静态代码分析
- 性能基准测试

### 7.2 系统质量
- 压力测试
- 安全测试
- 兼容性测试
- 可用性测试

### 7.3 交付质量
- 功能验收测试
- 用户体验测试
- 文档完整性检查
- 部署验证

## 8. 时间节点

- **第1周**: 项目搭建和核心支付模块
- **第2周**: 订单管理和对账模块
- **第3周**: API接口和测试优化
- **第4周**: 部署上线和文档完善

## 9. 成功标准

### 9.1 功能标准
- 支持 Native 支付
- 订单管理完整
- 对账功能正常
- API 接口稳定

### 9.2 性能标准
- 接口响应时间 < 500ms
- 并发支持 1000+ QPS
- 系统可用性 > 99.9%
- 数据一致性保证

### 9.3 质量标准
- 代码覆盖率 > 80%
- 无严重安全漏洞
- 文档完整准确
- 部署自动化
