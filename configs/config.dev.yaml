# 开发环境配置
server:
  port: 8080
  mode: debug

database:
  host: localhost
  port: 3306
  username: root
  password: "123456"
  database: pay_core
  charset: utf8mb4
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600

redis:
  addr: localhost:6379
  password: ""
  db: 0

payment:
  alipay:
    app_id: "2016091200494382"  # 沙箱应用ID
    private_key: |
      -----BEGIN RSA PRIVATE KEY-----
      MIIEpAIBAAKCAQEA4f5wg5l2hKsTeNem/V41fGnJm6gOdrj8ym3rFkEjWT0vs9GG
      mHmg4YvYGi4X+X49oAUVyR+HdHSHjvx+xdwQxtfn5RF6s2PBZ3oAM1ZTDX3f9atv
      QcaXdM5nI6OH0lRfbBhXYF4xmJi+k/angdHYVBUoWqeIu1RM9z/tbTj8j1cFnxm9
      s3e0kJCHL7sn96djMqgh5AAUJ7b1fd5X6Xn1/cLMDu1aJjlqFGqL+CmXiF2hcKgS
      XhOHxriQXfOjc5t1kJ2qh3XA+Q1TdMSFpKKS2UlQ7BHjm0LfMGmngMudfh2pXfk5
      kdX6WJDHK2ZHGgHJ0FBFbcSn3qHwPQs/TRJd1QIDAQABAoIBAEWmICXx/PlPvqZ4
      zdDqAxDwUnoEehb8zGiCxJhJqEe06B5dYNSM28EZvwYlKoJ3GqQqkqLcqfYjJmbl
      -----END RSA PRIVATE KEY-----
    public_key: |
      -----BEGIN PUBLIC KEY-----
      MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuWJKrQ6SWvS6niI+4vEi
      yxyuGe0AOAoRhKrfVxy2Ub7v83d8C8aq9TYHnwlaUHzjbaXHtyiGJr8BXuEkZqTX
      39gsrUjbqkCt5CqcWpnBqcU5in/g7XJ2upBBnsMhD1jEAlivtof681ZdHBHei6RQ
      KfAcejdJ3Y8ELnXx9IUddBF/AcuVjbAV+/FqFk7HpAN6RV8uJEx3IgckRU3Eafkn
      7DsoVweCRXD7A5OpUXNQiJuS/rKSiKSUBtDlXBnANBRTbVvveROHHEfxArzfyHdw
      7rEMhNU3GtBDHVoHXvfQA7uPiCEXvdUeFyGOb2f4iwWfN5YtjL4vdulrLp5aPqHd
      wQIDAQAB
      -----END PUBLIC KEY-----
    is_prod: false

  wechat:
    app_id: "wx1234567890123456"
    mch_id: "1234567890"
    api_key: "your_wechat_api_key_here"
    is_prod: false

log:
  level: debug
  format: json
  output: stdout


