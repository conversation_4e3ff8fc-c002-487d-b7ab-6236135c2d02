# 支付核心系统配置示例文件
# 复制此文件为 config.yaml 并填入真实配置

server:
  port: 8080
  mode: debug  # debug, release, test

database:
  host: localhost
  port: 3306
  username: root
  password: "your_password"
  database: pay_core
  charset: utf8mb4
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600

redis:
  addr: localhost:6379
  password: ""
  db: 0

# 客户端认证配置
auth:
  app_secret: "your_app_secret_32_chars_long"
  timestamp_tolerance: 300  # 5分钟（秒）
  nonce_ttl: 600s          # 10分钟（Duration格式）

# 支付配置
payment:
  alipay:
    app_id: "2021000000000000"           # 支付宝应用ID
    private_key: |                       # 支付宝应用私钥（RSA2格式，多行字符串）
      -----BEGIN RSA PRIVATE KEY-----
      MIIEpAIBAAKCAQEA...
      -----END RSA PRIVATE KEY-----
    public_key: |                        # 支付宝公钥（用于验签，多行字符串）
      -----BEGIN PUBLIC KEY-----
      MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A...
      -----END PUBLIC KEY-----
    is_prod: false                       # 是否生产环境（false=沙箱环境）

  wechat:
    app_id: "wx1234567890abcdef"         # 微信应用ID
    mch_id: "1234567890"                 # 商户号
    is_prod: false                       # 是否生产环境（false=沙箱环境）

    # V3 API必需配置
    apiv3_key: "your_v3_api_key_32_chars"    # V3 API密钥（32位）
    serial_no: "1234567890ABCDEF1234567890ABCDEF12345678"  # 商户证书序列号（40位十六进制）

    # 商户私钥配置方式一：直接配置内容（推荐）
    private_key: |                       # 商户私钥内容（PEM格式）
      -----BEGIN PRIVATE KEY-----
      MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
      -----END PRIVATE KEY-----

    # 商户私钥配置方式二：使用文件路径（与private_key二选一）
    private_key_path: "/path/to/apiclient_key.pem"  # 商户私钥文件路径

    # 微信支付平台证书配置（用于验签，可选但推荐）
    platform_cert_path: "/path/to/wechatpay_platform.pem"  # 微信支付平台证书文件路径
    platform_cert: |                    # 微信支付平台证书内容（PEM格式，与文件路径二选一）
      -----BEGIN CERTIFICATE-----
      MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
      -----END CERTIFICATE-----
    platform_cert_serial_no: "1234567890ABCDEF1234567890ABCDEF12345678"  # 微信支付平台证书序列号

log:
  level: info      # debug, info, warn, error
  format: json     # json, text
  output: stdout   # stdout, stderr, file



# 定时任务配置
scheduler:
  # 关闭过期订单任务
  close_expired_orders:
    enabled: true
    cron: "0 */5 * * * *"              # 每5分钟执行一次
    timeout_minutes: 5                  # 任务超时时间
    batch_size: 100                     # 每批处理订单数
    scan_days: 7                        # 扫描最近7天的订单
    lock_key: "pay_core:close_expired_orders"
    lock_ttl: 300                       # 分布式锁TTL（秒）
    
  # 每日对账任务
  daily_reconcile:
    enabled: true
    cron: "0 0 2 * * *"                # 每天凌晨2点执行
    timeout_minutes: 30                 # 任务超时时间
    channels: ["alipay", "wechat"]      # 对账渠道
    delay_days: 1                       # 对账延迟天数（T+1）
    lock_key: "pay_core:daily_reconcile"
    lock_ttl: 1800                      # 分布式锁TTL（秒）

# 业务配置
business:
  # 订单配置
  order:
    default_expire_minutes: 120         # 默认订单过期时间（分钟）
    max_expire_minutes: 1440           # 最大订单过期时间（24小时）
    min_amount: 0.01                   # 最小支付金额（元）
    max_amount: 50000.00               # 最大支付金额（元）
    
  # 回调配置
  notify:
    max_retry_times: 5                 # 最大重试次数
    retry_intervals: [1, 3, 5, 10, 30] # 重试间隔（分钟）
    timeout_seconds: 30                # 回调超时时间（秒）
    
  # 对账配置
  reconcile:
    batch_size: 1000                   # 每批对账记录数
    max_diff_amount: 0.01              # 最大允许差异金额（元）

# 监控配置
monitoring:
  metrics:
    enabled: true
    path: "/metrics"                   # Prometheus metrics路径
    
  health:
    enabled: true
    path: "/health"                    # 健康检查路径
    
  # 告警配置
  alerts:
    payment_success_rate_threshold: 0.95  # 支付成功率告警阈值
    callback_success_rate_threshold: 0.98 # 回调成功率告警阈值
    response_time_threshold: 5000         # 响应时间告警阈值（毫秒）

# 安全配置
security:
  # IP白名单（支付平台回调IP）
  callback_whitelist:
    alipay:
      - "************/24"
      - "************/24"
    wechat:
      - "*************/24"
      - "************/24"
      
  # 签名配置
  signature:
    algorithm: "SHA256"                # 签名算法
    header_name: "X-Signature"        # 签名头名称
    
  # 限流配置
  rate_limit:
    enabled: true
    requests_per_minute: 1000          # 每分钟请求数限制
    burst_size: 100                    # 突发请求数
