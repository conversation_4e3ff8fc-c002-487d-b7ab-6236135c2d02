# 微信支付配置字段和证书文件说明

## 概述

本文档详细说明微信支付相关的配置字段和证书文件的用途及配置方法。PayCore 系统专注于微信支付，提供完整的微信支付V3 API集成。

## 配置字段说明

### 微信支付配置 (wechat)

在 `config.yaml` 文件中的微信支付配置：

```yaml
wechat:
  app_id: "your_app_id"                    # 微信公众号或小程序的 AppID
  mch_id: "your_merchant_id"               # 微信支付商户号
  api_key: "your_api_key"                  # 微信支付 API 密钥
  cert_path: "configs/1264055901_20250822_cert/apiclient_cert.pem"  # 商户证书文件路径
  key_path: "configs/1264055901_20250822_cert/apiclient_key.pem"    # 商户私钥文件路径
  notify_url: "https://your-domain.com/api/v1/notify/wechat"        # 支付结果通知地址
  sandbox: false                           # 是否使用沙箱环境
```

#### 字段详细说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `app_id` | string | 是 | 微信公众号或小程序的 AppID，用于标识应用 |
| `mch_id` | string | 是 | 微信支付分配的商户号，用于标识商户 |
| `api_key` | string | 是 | 微信支付 API 密钥，用于签名验证 |
| `cert_path` | string | 是 | 商户 API 证书文件路径，用于双向 SSL 认证 |
| `key_path` | string | 是 | 商户 API 私钥文件路径，与证书配对使用 |
| `notify_url` | string | 是 | 支付结果异步通知地址，必须是 HTTPS |
| `sandbox` | bool | 否 | 是否使用沙箱环境，默认 false |

## 证书文件说明

### 1. 微信支付公钥 (`configs/pub_key.pem`)

- **用途**: 微信支付平台的公钥，用于验证微信支付返回数据的签名
- **格式**: PEM 格式的 RSA 公钥
- **获取方式**: 从微信支付商户平台下载或通过 API 获取
- **使用场景**: 
  - 验证支付结果通知的签名
  - 验证查询接口返回数据的签名
  - 确保数据来源的真实性

### 2. 商户证书 (`configs/1264055901_20250822_cert/apiclient_cert.pem`)

- **用途**: 商户的 API 证书，用于调用需要证书的微信支付接口
- **格式**: PEM 格式的 X.509 证书
- **获取方式**: 从微信支付商户平台下载
- **使用场景**:
  - 企业付款到零钱
  - 退款接口调用
  - 撤销订单接口
  - 其他需要双向 SSL 认证的接口

### 3. 商户私钥 (`configs/1264055901_20250822_cert/apiclient_key.pem`)

- **用途**: 商户证书对应的私钥文件
- **格式**: PEM 格式的 RSA 私钥
- **获取方式**: 与商户证书一起从微信支付商户平台下载
- **安全要求**: 
  - 严格保密，不得泄露
  - 建议设置文件权限为 600
  - 生产环境建议使用密钥管理服务

### 4. 商户证书 P12 格式 (`configs/1264055901_20250822_cert/apiclient_cert.p12`)

- **用途**: PKCS#12 格式的商户证书，包含证书和私钥
- **格式**: P12/PFX 二进制格式
- **使用场景**: 某些 SDK 或工具可能需要此格式

## 证书文件管理

### 文件权限设置

```bash
# 设置证书文件权限
chmod 600 configs/1264055901_20250822_cert/apiclient_key.pem
chmod 644 configs/1264055901_20250822_cert/apiclient_cert.pem
chmod 644 configs/pub_key.pem
```

### 证书有效期

- 商户证书通常有效期为 1 年
- 需要在到期前及时更新
- 建议设置监控提醒证书到期时间

### 环境区分

```yaml
# 开发环境
wechat:
  sandbox: true
  cert_path: "configs/sandbox_cert/apiclient_cert.pem"
  key_path: "configs/sandbox_cert/apiclient_key.pem"

# 生产环境  
wechat:
  sandbox: false
  cert_path: "configs/1264055901_20250822_cert/apiclient_cert.pem"
  key_path: "configs/1264055901_20250822_cert/apiclient_key.pem"
```

## 安全注意事项

1. **私钥保护**: 商户私钥文件必须严格保密，不得提交到代码仓库
2. **权限控制**: 设置适当的文件系统权限，限制访问
3. **传输安全**: 证书文件传输时使用加密通道
4. **定期更新**: 及时更新到期的证书文件
5. **备份管理**: 做好证书文件的安全备份

## 常见问题

### Q: 如何验证证书文件是否正确？

```bash
# 验证证书文件格式
openssl x509 -in configs/1264055901_20250822_cert/apiclient_cert.pem -text -noout

# 验证私钥文件格式
openssl rsa -in configs/1264055901_20250822_cert/apiclient_key.pem -check

# 验证证书和私钥是否匹配
openssl x509 -noout -modulus -in configs/1264055901_20250822_cert/apiclient_cert.pem | openssl md5
openssl rsa -noout -modulus -in configs/1264055901_20250822_cert/apiclient_key.pem | openssl md5
```

### Q: 证书文件路径配置错误怎么办？

检查配置文件中的路径是否正确，确保：
- 路径相对于项目根目录
- 文件确实存在
- 文件权限正确

### Q: 如何更新微信支付公钥？

微信支付公钥可能会定期更新，建议：
- 定期从微信支付平台获取最新公钥
- 实现公钥自动更新机制
- 支持多个公钥并存以平滑过渡