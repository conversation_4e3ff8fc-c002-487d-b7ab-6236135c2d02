package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"pay-core/internal/config"
	"pay-core/internal/handler"
	"pay-core/internal/middleware"
	"pay-core/internal/repository"
	"pay-core/internal/service"
	"pay-core/pkg/database"
	"pay-core/pkg/logger"
	"pay-core/pkg/payment"
	"pay-core/pkg/robot"
	"pay-core/pkg/types"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// @title Pay Core API
// @version 1.0
// @description 支付系统核心API
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey Api<PERSON>ey<PERSON>uth
// @in header
// @name Authorization
func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logger.Init(cfg.Log)

	// 初始化数据库
	db, err := database.NewMySQL(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 初始化Redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     cfg.Redis.Addr,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})

	// 测试Redis连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := rdb.Ping(ctx).Err(); err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}

	// 初始化支付客户端
	paymentClient, err := payment.NewClient(cfg.Payment)
	if err != nil {
		log.Fatalf("Failed to initialize payment client: %v", err)
	}

	// 初始化机器人服务客户端
	robotClient := robot.NewClient(robot.Config{
		BaseURL:   cfg.Robot.BaseURL,
		SharedKey: cfg.Robot.SharedKey,
		Timeout:   cfg.Robot.Timeout,
	})

	// 初始化Repository层
	appRepo := repository.NewAppRepository(db)
	paymentOrderRepo := repository.NewPaymentOrderRepository(db)
	refundOrderRepo := repository.NewRefundOrderRepository(db)
	paymentRecordRepo := repository.NewPaymentRecordRepository(db)
	refundRecordRepo := repository.NewRefundRecordRepository(db)
	notifyLogRepo := repository.NewNotifyLogRepository(db)
	reconciliationRepo := repository.NewReconciliationRepository(db)

	// 初始化Service层
	paymentService := service.NewPaymentService(paymentOrderRepo, refundOrderRepo, paymentRecordRepo, refundRecordRepo, notifyLogRepo, appRepo, paymentClient, robotClient, rdb)
	reconciliationService := service.NewReconciliationService(reconciliationRepo, paymentOrderRepo, paymentClient)
	schedulerService := service.NewSchedulerService(paymentService, reconciliationService, rdb, cfg.Scheduler)

	// 初始化Handler层
	paymentHandler := handler.NewPaymentHandler(paymentService, appRepo)
	reconciliationHandler := handler.NewReconciliationHandler(reconciliationService)

	// 设置Gin模式
	if cfg.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin引擎
	r := gin.New()

	// 添加中间件
	r.Use(middleware.Logger())
	r.Use(middleware.Recovery())
	r.Use(middleware.CORS())

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "ok",
			"time":   types.Now().Unix(),
		})
	})

	// API路由组
	v1 := r.Group("/api/v1")
	v1.Use(middleware.ClientAuth(rdb, appRepo)) // 使用客户端签名认证中间件
	{
		// 支付相关路由
		orders := v1.Group("/orders")
		{
			orders.POST("", paymentHandler.CreateOrder)
			orders.GET("/:order_no", paymentHandler.GetOrder)
			orders.POST("/:order_no/refund", paymentHandler.RefundOrder)
			orders.GET("/query", paymentHandler.QueryOrder)
		}

		// 对账相关路由
		reconciliation := v1.Group("/reconciliation")
		{
			reconciliation.POST("/start", reconciliationHandler.StartReconciliation)
			reconciliation.GET("/batches/:batchNo", reconciliationHandler.GetBatch)
			reconciliation.GET("/details", reconciliationHandler.GetDetails)
		}
	}

	// 回调路由（不需要认证，由支付平台调用）
	notify := r.Group("/notify")
	{
		notify.POST("/:channel", paymentHandler.Notify)
		notify.POST("/:channel/refund", paymentHandler.RefundNotify)
	}

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.Port),
		Handler: r,
	}

	// 启动定时任务服务
	if err := schedulerService.Start(); err != nil {
		log.Fatalf("Failed to start scheduler service: %v", err)
	}

	// 启动队列处理服务
	queueService := service.NewQueueService(rdb, paymentService)
	if err := queueService.Start(); err != nil {
		log.Fatalf("Failed to start queue service: %v", err)
	}

	// 启动服务器
	go func() {
		logger.Info("Starting server on port ", cfg.Server.Port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// 停止定时任务服务
	if err := schedulerService.Stop(); err != nil {
		logger.WithError(err).Error("Failed to stop scheduler service")
	}

	// 停止队列处理服务
	if err := queueService.Stop(); err != nil {
		logger.WithError(err).Error("Failed to stop queue service")
	}

	// 优雅关闭
	ctx, cancel = context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	logger.Info("Server exited")
}
