package main

import (
	"fmt"
	"log"

	"pay-core/internal/config"
	"pay-core/internal/model"
	"pay-core/pkg/database"

	"gorm.io/gorm"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db, err := database.NewMySQL(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 创建robot应用
	app := &model.MerchantApp{
		AppCode:   "robot",
		AppName:   "机器人应用",
		AppSecret: "7Hk9pJ2fTdR8sLq4wVbN5zKmG6xYcX3F",
		NotifyURL: "http://localhost:8080/notify/callback",
		Status:    true,
	}

	// 检查应用是否已存在
	var existingApp model.MerchantApp
	result := db.Where("app_code = ?", app.AppCode).First(&existingApp)
	if result.Error == nil {
		fmt.Printf("应用 %s 已存在，ID: %d\n", app.AppCode, existingApp.ID)
		fmt.Printf("AppSecret: %s\n", existingApp.AppSecret)
		return
	} else if result.Error != gorm.ErrRecordNotFound {
		log.Fatalf("查询应用失败: %v", result.Error)
	}

	// 创建应用
	if err := db.Create(app).Error; err != nil {
		log.Fatalf("创建应用失败: %v", err)
	}

	fmt.Printf("成功创建robot应用:\n")
	fmt.Printf("ID: %d\n", app.ID)
	fmt.Printf("AppCode: %s\n", app.AppCode)
	fmt.Printf("AppName: %s\n", app.AppName)
	fmt.Printf("AppSecret: %s\n", app.AppSecret)
	fmt.Printf("Status: %t\n", app.Status)
}