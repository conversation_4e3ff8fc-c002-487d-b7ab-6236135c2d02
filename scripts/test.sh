#!/bin/bash

# 测试脚本
echo "=== Pay Core API 测试 ==="

BASE_URL="http://localhost:8080"

# 测试健康检查
echo "1. 测试健康检查..."
curl -s "$BASE_URL/health" | jq .

echo -e "\n2. 测试创建支付订单..."
curl -s -X POST "$BASE_URL/api/v1/orders" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test_token" \
  -d '{
    "merchant_order_no": "TEST_ORDER_001",
    "subject": "测试商品",
    "body": "这是一个测试商品",
    "amount": "0.01",
    "payment_method": "native",
    "payment_channel": "alipay",
    "expire_minutes": 30
  }' | jq .

echo -e "\n=== 测试完成 ==="
