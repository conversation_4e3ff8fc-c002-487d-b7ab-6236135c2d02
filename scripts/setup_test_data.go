package main

import (
	"fmt"
	"io/ioutil"
	"log"

	"pay-core/internal/config"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 构建DSN
	dsn := cfg.Database.GetDSN()

	// 配置GORM（不使用自动迁移）
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), gormConfig)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 读取并执行建表SQL
	sqlContent, err := ioutil.ReadFile("scripts/create_test_data.sql")
	if err != nil {
		log.Fatalf("Failed to read SQL file: %v", err)
	}

	// 执行建表SQL
	if err := db.Exec(string(sqlContent)).Error; err != nil {
		log.Fatalf("Failed to create table: %v", err)
	}
	fmt.Println("表创建成功！")

	// 插入测试数据
	insertSQL := `INSERT INTO merchant_apps (app_code, app_name, app_secret, notify_url, status, created_at, updated_at) 
	VALUES (?, ?, ?, ?, ?, NOW(), NOW()) 
	ON DUPLICATE KEY UPDATE 
	app_name = VALUES(app_name), 
	app_secret = VALUES(app_secret), 
	notify_url = VALUES(notify_url), 
	status = VALUES(status), 
	updated_at = NOW()`

	if err := db.Exec(insertSQL, "APP_001", "测试应用", "pay-core-client-secret-2024", "http://localhost:8080/notify/callback", 1).Error; err != nil {
		log.Fatalf("Failed to insert test data: %v", err)
	}
	fmt.Println("测试数据插入成功！")
	
	// 验证数据
	var count int64
	if err := db.Raw("SELECT COUNT(*) FROM merchant_apps WHERE app_code = 'APP_001'").Scan(&count).Error; err != nil {
		log.Printf("Failed to verify data: %v", err)
	} else {
		fmt.Printf("验证：找到 %d 条 APP_001 记录\n", count)
	}
}