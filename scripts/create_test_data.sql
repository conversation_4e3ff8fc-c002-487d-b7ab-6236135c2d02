CREATE TABLE IF NOT EXISTS `merchant_apps` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `app_code` varchar(64) NOT NULL COMMENT '应用编码',
  `app_name` varchar(128) NOT NULL COMMENT '应用名称',
  `app_secret` varchar(256) NOT NULL COMMENT '应用密钥',
  `notify_url` varchar(512) DEFAULT NULL COMMENT '支付结果回调地址',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_merchant_apps_app_code` (`app_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;