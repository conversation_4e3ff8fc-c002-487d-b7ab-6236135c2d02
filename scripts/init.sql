-- 创建数据库
CREATE DATABASE IF NOT EXISTS pay_core DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE pay_core;

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `password_hash` varchar(255) NOT NULL COMMENT '密码哈希',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 商户表
CREATE TABLE IF NOT EXISTS `merchants` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '商户ID',
  `merchant_no` varchar(32) NOT NULL COMMENT '商户号',
  `merchant_name` varchar(100) NOT NULL COMMENT '商户名称',
  `contact_name` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `api_key` varchar(64) NOT NULL COMMENT 'API密钥',
  `api_secret` varchar(128) NOT NULL COMMENT 'API密钥',
  `notify_url` varchar(255) DEFAULT NULL COMMENT '异步通知地址',
  `return_url` varchar(255) DEFAULT NULL COMMENT '同步返回地址',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_merchant_no` (`merchant_no`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户表';

-- 支付订单表
CREATE TABLE IF NOT EXISTS `payment_orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `merchant_id` bigint(20) unsigned NOT NULL COMMENT '商户ID',
  `merchant_order_no` varchar(64) NOT NULL COMMENT '商户订单号',
  `user_id` bigint(20) unsigned DEFAULT NULL COMMENT '用户ID',
  `subject` varchar(256) NOT NULL COMMENT '订单标题',
  `body` varchar(512) DEFAULT NULL COMMENT '订单描述',
  `amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `currency` varchar(3) NOT NULL DEFAULT 'CNY' COMMENT '货币类型',
  `payment_method` varchar(20) NOT NULL COMMENT '支付方式：native,h5,app',
  `payment_channel` varchar(20) NOT NULL COMMENT '支付渠道：alipay,wechat,qq',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '订单状态：0-待支付，1-支付成功，2-支付失败，3-已关闭，4-已退款',
  `qr_code` text DEFAULT NULL COMMENT '支付二维码',
  `prepay_id` varchar(64) DEFAULT NULL COMMENT '预支付ID',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '第三方交易号',
  `paid_at` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  `expired_at` timestamp NOT NULL COMMENT '过期时间',
  `notify_url` varchar(255) DEFAULT NULL COMMENT '异步通知地址',
  `return_url` varchar(255) DEFAULT NULL COMMENT '同步返回地址',
  `extra_data` json DEFAULT NULL COMMENT '扩展数据',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  UNIQUE KEY `uk_merchant_order` (`merchant_id`, `merchant_order_no`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_payment_channel` (`payment_channel`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付订单表';

-- 支付记录表
CREATE TABLE IF NOT EXISTS `payment_records` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `transaction_id` varchar(64) NOT NULL COMMENT '第三方交易号',
  `payment_channel` varchar(20) NOT NULL COMMENT '支付渠道',
  `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `currency` varchar(3) NOT NULL DEFAULT 'CNY' COMMENT '货币类型',
  `status` tinyint(4) NOT NULL COMMENT '支付状态：1-成功，2-失败',
  `channel_response` json DEFAULT NULL COMMENT '渠道响应数据',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_transaction_id` (`transaction_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_payment_channel` (`payment_channel`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付记录表';

-- 退款订单表
CREATE TABLE IF NOT EXISTS `refund_orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '退款ID',
  `refund_no` varchar(32) NOT NULL COMMENT '退款单号',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '原订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '原订单号',
  `merchant_id` bigint(20) unsigned NOT NULL COMMENT '商户ID',
  `refund_amount` decimal(10,2) NOT NULL COMMENT '退款金额',
  `refund_reason` varchar(255) DEFAULT NULL COMMENT '退款原因',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '退款状态：0-处理中，1-成功，2-失败',
  `refund_id` varchar(64) DEFAULT NULL COMMENT '第三方退款号',
  `refunded_at` timestamp NULL DEFAULT NULL COMMENT '退款时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_refund_no` (`refund_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款订单表';

-- 对账记录表
CREATE TABLE IF NOT EXISTS `reconciliation_records` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '对账ID',
  `batch_no` varchar(32) NOT NULL COMMENT '对账批次号',
  `reconcile_date` date NOT NULL COMMENT '对账日期',
  `payment_channel` varchar(20) NOT NULL COMMENT '支付渠道',
  `total_count` int(11) NOT NULL DEFAULT '0' COMMENT '总笔数',
  `total_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '总金额',
  `success_count` int(11) NOT NULL DEFAULT '0' COMMENT '成功笔数',
  `success_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '成功金额',
  `diff_count` int(11) NOT NULL DEFAULT '0' COMMENT '差异笔数',
  `diff_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '差异金额',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '对账状态：0-处理中，1-成功，2-有差异',
  `file_path` varchar(255) DEFAULT NULL COMMENT '对账文件路径',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_batch_no` (`batch_no`),
  KEY `idx_reconcile_date` (`reconcile_date`),
  KEY `idx_payment_channel` (`payment_channel`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对账记录表';

-- 对账差异表
CREATE TABLE IF NOT EXISTS `reconciliation_diffs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '差异ID',
  `batch_no` varchar(32) NOT NULL COMMENT '对账批次号',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '第三方交易号',
  `diff_type` tinyint(4) NOT NULL COMMENT '差异类型：1-本地有渠道无，2-渠道有本地无，3-金额不一致，4-状态不一致',
  `local_amount` decimal(10,2) DEFAULT NULL COMMENT '本地金额',
  `channel_amount` decimal(10,2) DEFAULT NULL COMMENT '渠道金额',
  `local_status` tinyint(4) DEFAULT NULL COMMENT '本地状态',
  `channel_status` varchar(20) DEFAULT NULL COMMENT '渠道状态',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '处理状态：0-待处理，1-已处理，2-忽略',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_diff_type` (`diff_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对账差异表';

-- 插入测试数据
INSERT INTO `users` (`username`, `email`, `password_hash`, `status`) VALUES
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFVMLVZqpjBKt7IbdqRo.L2', 1),
('testuser', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFVMLVZqpjBKt7IbdqRo.L2', 1);

INSERT INTO `merchants` (`merchant_no`, `merchant_name`, `contact_name`, `contact_email`, `api_key`, `api_secret`, `status`) VALUES
('M20240101001', '测试商户', '张三', '<EMAIL>', 'test_api_key_123456', 'test_api_secret_abcdef', 1);
