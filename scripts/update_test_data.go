package main

import (
	"fmt"
	"log"

	"pay-core/internal/config"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 构建DSN
	dsn := cfg.Database.GetDSN()

	// 配置GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), gormConfig)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 更新或插入robot应用数据
	updateSQL := `INSERT INTO merchant_apps (app_code, app_name, app_secret, notify_url, status, created_at, updated_at) 
	VALUES (?, ?, ?, ?, ?, NOW(), NOW()) 
	ON DUPLICATE KEY UPDATE 
	app_name = VALUES(app_name), 
	app_secret = VALUES(app_secret), 
	notify_url = VALUES(notify_url), 
	status = VALUES(status), 
	updated_at = NOW()`

	if err := db.Exec(updateSQL, "robot", "机器人应用", "7Hk9pJ2fTdR8sLq4wVbN5zKmG6xYcX3F", "http://localhost:8080/notify/callback", 1).Error; err != nil {
		log.Fatalf("Failed to insert/update robot app data: %v", err)
	}
	fmt.Println("robot应用数据更新成功！")
	
	// 验证数据
	var count int64
	if err := db.Raw("SELECT COUNT(*) FROM merchant_apps WHERE app_code = 'robot'").Scan(&count).Error; err != nil {
		log.Printf("Failed to verify data: %v", err)
	} else {
		fmt.Printf("验证：找到 %d 条 robot 记录\n", count)
	}

	// 显示所有应用
	var apps []struct {
		AppCode string `json:"app_code"`
		AppName string `json:"app_name"`
		Status  bool   `json:"status"`
	}
	if err := db.Raw("SELECT app_code, app_name, status FROM merchant_apps").Scan(&apps).Error; err != nil {
		log.Printf("Failed to query apps: %v", err)
	} else {
		fmt.Println("当前所有应用：")
		for _, app := range apps {
			fmt.Printf("- AppCode: %s, AppName: %s, Status: %t\n", app.AppCode, app.AppName, app.Status)
		}
	}
}