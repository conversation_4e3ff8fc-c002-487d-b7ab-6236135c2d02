#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import yaml
import sys
import os

def validate_openapi_yaml(file_path):
    """验证OpenAPI YAML文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            doc = yaml.safe_load(f)
        
        print('✅ YAML语法检查通过')
        
        # 基本信息
        print(f'📊 OpenAPI版本: {doc.get("openapi", "未知")}')
        print(f'📊 API标题: {doc.get("info", {}).get("title", "未知")}')
        print(f'📊 API版本: {doc.get("info", {}).get("version", "未知")}')
        print(f'📊 API描述: {doc.get("info", {}).get("description", "未知")}')
        
        # 统计路径数量
        paths = doc.get('paths', {})
        print(f'📊 API路径数量: {len(paths)}')
        
        # 统计HTTP方法数量
        method_count = 0
        for path, methods in paths.items():
            method_count += len([m for m in methods.keys() if m in ['get', 'post', 'put', 'delete', 'patch']])
        print(f'📊 API方法数量: {method_count}')
        
        # 统计数据模型数量
        schemas = doc.get('components', {}).get('schemas', {})
        print(f'📊 数据模型数量: {len(schemas)}')
        
        # 统计标签数量
        tags = doc.get('tags', [])
        print(f'📊 API标签数量: {len(tags)}')
        
        # 统计服务器数量
        servers = doc.get('servers', [])
        print(f'📊 服务器配置数量: {len(servers)}')
        
        # 检查安全配置
        security_schemes = doc.get('components', {}).get('securitySchemes', {})
        print(f'📊 安全方案数量: {len(security_schemes)}')
        
        print('')
        print('[object Object]路径列表:')
        for path, methods in paths.items():
            method_list = [m.upper() for m in methods.keys() if m in ['get', 'post', 'put', 'delete', 'patch']]
            print(f'  {path} - {", ".join(method_list)}')
        
        print('')
        print('📋 数据模型列表:')
        for schema_name in schemas.keys():
            print(f'  - {schema_name}')
        
        print('')
        print('📋 API标签列表:')
        for tag in tags:
            name = tag.get('name', '未知')
            description = tag.get('description', '')
            print(f'  - {name}: {description}')
        
        print('')
        print('🎉 OpenAPI文档验证完成！')
        return True
        
    except yaml.YAMLError as e:
        print(f'❌ YAML语法错误: {e}')
        return False
    except FileNotFoundError:
        print(f'❌ 文件不存在: {file_path}')
        return False
    except Exception as e:
        print(f'❌ 验证错误: {e}')
        return False

def main():
    """主函数"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(script_dir)
    openapi_file = os.path.join(project_dir, 'docs', 'openapi.yaml')
    
    print('🔍 验证OpenAPI文档...')
    print(f'文件路径: {openapi_file}')
    print('')
    
    if validate_openapi_yaml(openapi_file):
        print('')
        print('📖 查看文档的方法：')
        print('1. 在线查看: https://editor.swagger.io/ (导入 docs/openapi.yaml)')
        print('2. 本地查看: docker run -p 8081:8080 -e SWAGGER_JSON=/openapi.yaml -v $(pwd)/docs/openapi.yaml:/openapi.yaml swaggerapi/swagger-ui')
        print('3. 生成客户端: openapi-generator-cli generate -i docs/openapi.yaml -g <language> -o ./generated/<language>-client')
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == '__main__':
    main()
