#!/bin/bash

# OpenAPI文档验证脚本
# 用于验证OpenAPI YAML文件的有效性

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
OPENAPI_FILE="$PROJECT_DIR/docs/openapi.yaml"

echo "🔍 验证OpenAPI文档..."
echo "文件路径: $OPENAPI_FILE"

# 检查文件是否存在
if [ ! -f "$OPENAPI_FILE" ]; then
    echo "❌ OpenAPI文件不存在: $OPENAPI_FILE"
    exit 1
fi

# 检查是否安装了swagger-codegen或openapi-generator
if command -v swagger-codegen &> /dev/null; then
    echo "✅ 使用swagger-codegen验证..."
    swagger-codegen validate -i "$OPENAPI_FILE"
    echo "✅ swagger-codegen验证通过"
elif command -v openapi-generator-cli &> /dev/null; then
    echo "✅ 使用openapi-generator-cli验证..."
    openapi-generator-cli validate -i "$OPENAPI_FILE"
    echo "✅ openapi-generator-cli验证通过"
elif command -v docker &> /dev/null; then
    echo "✅ 使用Docker中的swagger-codegen验证..."
    docker run --rm -v "$PROJECT_DIR:/workspace" swaggerapi/swagger-codegen-cli validate -i /workspace/docs/openapi.yaml
    echo "✅ Docker swagger-codegen验证通过"
else
    echo "⚠️  未找到验证工具，尝试基本的YAML语法检查..."
    
    # 使用Python检查YAML语法
    if command -v python3 &> /dev/null; then
        python3 -c "
import yaml
import sys

try:
    with open('$OPENAPI_FILE', 'r', encoding='utf-8') as f:
        yaml.safe_load(f)
    print('✅ YAML语法检查通过')
except yaml.YAMLError as e:
    print(f'❌ YAML语法错误: {e}')
    sys.exit(1)
except Exception as e:
    print(f'❌ 文件读取错误: {e}')
    sys.exit(1)
"
    elif command -v python &> /dev/null; then
        python -c "
import yaml
import sys

try:
    with open('$OPENAPI_FILE', 'r') as f:
        yaml.safe_load(f)
    print('✅ YAML语法检查通过')
except yaml.YAMLError as e:
    print('❌ YAML语法错误: ' + str(e))
    sys.exit(1)
except Exception as e:
    print('❌ 文件读取错误: ' + str(e))
    sys.exit(1)
"
    else
        echo "⚠️  无法进行详细验证，请安装以下工具之一："
        echo "   - swagger-codegen"
        echo "   - openapi-generator-cli"
        echo "   - Docker"
        echo "   - Python (with PyYAML)"
    fi
fi

echo ""
echo "📊 文档统计信息:"
echo "文件大小: $(du -h "$OPENAPI_FILE" | cut -f1)"
echo "行数: $(wc -l < "$OPENAPI_FILE")"

# 统计API路径数量
if command -v grep &> /dev/null; then
    PATH_COUNT=$(grep -c "^  /" "$OPENAPI_FILE" || echo "0")
    echo "API路径数量: $PATH_COUNT"
fi

# 统计数据模型数量
if command -v grep &> /dev/null; then
    SCHEMA_COUNT=$(grep -c "^    [A-Z].*:" "$OPENAPI_FILE" | head -1 || echo "0")
    echo "数据模型数量: 约$SCHEMA_COUNT个"
fi

echo ""
echo "🎉 OpenAPI文档验证完成！"
echo ""
echo "📖 查看文档的方法："
echo "1. 在线查看: https://editor.swagger.io/ (导入 docs/openapi.yaml)"
echo "2. 本地查看: docker run -p 8081:8080 -e SWAGGER_JSON=/openapi.yaml -v \$(pwd)/docs/openapi.yaml:/openapi.yaml swaggerapi/swagger-ui"
echo "3. 生成客户端: openapi-generator-cli generate -i docs/openapi.yaml -g <language> -o ./generated/<language>-client"
