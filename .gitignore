# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go build command
/pay-core
/cmd/server/server
/cmd/server/main

# Go workspace file
go.work

# Dependency directories (remove the comment below to include it)
# vendor/

# Go module cache
/go/pkg/mod/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~
.codebuddy/
.history/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Log files
*.log
logs/

# Configuration files with sensitive data
configs/config.prod.yaml
configs/config.local.yaml
.env
.env.local
.env.production

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
*.tmp

# Build artifacts
build/
dist/
bin/

# Java compiled files
*.class
examples/*.class

# Coverage reports
coverage.out
coverage.html

# Docker volumes
docker-data/

# Redis dump
dump.rdb

# Application specific
uploads/
static/uploads/