# Pay Core - 支付系统核心

基于 Golang 和 gopay 构建的现代化支付系统，支持多种支付方式和完整的订单管理、对账功能。

## 功能特性

- 🚀 **高性能**: 基于 Gin 框架，支持高并发处理
- 💳 **多支付渠道**: 支持支付宝、微信支付等主流支付方式
- 📱 **Native支付**: 专注于扫码支付场景
- 📊 **订单管理**: 完整的订单生命周期管理
- 🔄 **自动对账**: 支持自动下载账单和对账处理
- 🔐 **安全可靠**: JWT认证、签名验证、数据加密
- 📈 **可扩展**: 微服务架构，支持水平扩展
- 🐳 **容器化**: Docker 支持，一键部署

## 技术栈

- **语言**: Go 1.21+
- **框架**: Gin
- **数据库**: MySQL 8.0+
- **缓存**: Redis 7.0+
- **支付SDK**: gopay
- **ORM**: GORM
- **配置**: Viper
- **日志**: Logrus
- **认证**: JWT

## 项目结构

```
pay-core/
├── cmd/                    # 应用入口
│   └── server/
│       └── main.go
├── internal/               # 内部包
│   ├── config/            # 配置管理
│   ├── handler/           # HTTP处理器
│   ├── service/           # 业务逻辑
│   ├── repository/        # 数据访问
│   ├── model/             # 数据模型
│   └── middleware/        # 中间件
├── pkg/                   # 公共包
│   ├── payment/           # 支付相关
│   ├── database/          # 数据库
│   └── logger/            # 日志
├── configs/               # 配置文件
├── docs/                  # 文档
├── scripts/               # 脚本
├── deployments/           # 部署配置
└── test/                  # 测试
```

## 快速开始

### 环境要求

- Go 1.21+
- MySQL 8.0+
- Redis 7.0+
- Docker & Docker Compose (可选)

### 使用 Docker Compose (推荐)

1. 克隆项目
```bash
git clone <repository-url>
cd pay-core
```

2. 启动服务
```bash
docker-compose up -d
```

3. 检查服务状态
```bash
curl http://localhost:8080/health
```

### 本地开发

1. 安装依赖
```bash
go mod download
```

2. 配置数据库和Redis
```bash
# 启动MySQL和Redis
docker-compose up -d mysql redis
```

3. 修改配置文件
```bash
cp configs/config.yaml configs/config.local.yaml
# 编辑配置文件，设置数据库连接等信息
```

4. 运行应用
```bash
go run cmd/server/main.go
```

## API 文档

### 认证方式

本系统使用API签名认证，需要在请求头中包含：
- `X-API-Key`: 客户端ID
- `X-Timestamp`: 时间戳
- `X-Nonce`: 随机数
- `X-Signature`: HMAC-SHA256签名

### 支付管理

#### 创建支付订单
```http
POST /api/v1/orders
Content-Type: application/json
X-API-Key: your_client_id
X-Timestamp: **********
X-Nonce: random_string
X-Signature: generated_signature

{
  "app_order_no": "ORDER_123456",
  "amount": 100.50,
  "subject": "测试商品",
  "body": "这是一个测试订单",
  "pay_channel": "alipay",
  "app_user_id": "user_123"
}
```

### 支付相关

#### 创建支付订单
```http
POST /api/v1/payments/orders
Content-Type: application/json
X-API-Key: your-api-key

{
  "merchant_order_no": "ORDER_20240101_001",
  "subject": "测试商品",
  "body": "这是一个测试商品",
  "amount": "0.01",
  "payment_method": "native",
  "payment_channel": "alipay",
  "expire_minutes": 30
}
```

#### 查询订单状态
```http
GET /api/v1/payments/orders/{orderNo}
X-API-Key: your-api-key
```

#### 申请退款
```http
POST /api/v1/payments/orders/{orderNo}/refund
Content-Type: application/json
X-API-Key: your-api-key

{
  "refund_amount": "0.01",
  "refund_reason": "用户申请退款"
}
```

## 配置说明

主要配置项说明：

```yaml
# 服务器配置
server:
  port: 8080          # 服务端口
  mode: debug         # 运行模式: debug/release

# 数据库配置
database:
  host: localhost     # 数据库地址
  port: 3306         # 数据库端口
  username: root     # 用户名
  password: ""       # 密码
  database: pay_core # 数据库名

# 支付配置
payment:
  alipay:
    app_id: ""        # 支付宝应用ID
    private_key: ""   # 应用私钥
    public_key: ""    # 支付宝公钥
    is_prod: false    # 是否生产环境
```

## 部署

### Docker 部署

1. 构建镜像
```bash
docker build -t pay-core:latest .
```

2. 运行容器
```bash
docker run -d \
  --name pay-core \
  -p 8080:8080 \
  -e PAY_CORE_DATABASE_HOST=your-db-host \
  -e PAY_CORE_DATABASE_PASSWORD=your-db-password \
  pay-core:latest
```

### Kubernetes 部署

参考 `deployments/k8s/` 目录下的配置文件。

## 开发指南

### 添加新的支付渠道

1. 在 `pkg/payment/client.go` 中添加新的客户端
2. 实现相应的接口方法
3. 在配置文件中添加相关配置
4. 更新文档和测试

### 数据库迁移

使用 GORM 的自动迁移功能，或者手动执行 SQL 脚本。

### 测试

```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/service

# 生成测试覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

## 监控和日志

- 应用日志输出到标准输出，支持 JSON 格式
- 支持 Prometheus 指标收集
- 可集成 Jaeger 进行链路追踪

## 安全考虑

- 所有敏感配置使用环境变量
- API 接口使用签名验证
- 支付回调验证签名
- 数据库连接使用 TLS
- 定期更新依赖包

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交 Issue 或联系维护者。
