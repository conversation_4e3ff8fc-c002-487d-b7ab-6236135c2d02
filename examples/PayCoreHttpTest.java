import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PayCore HTTP请求测试类
 * 使用PayCoreSignUtil工具类进行签名，然后发送真实的HTTP请求
 */
public class PayCoreHttpTest {
    
    private static final String BASE_URL = "http://localhost:8080";
    // 用户ID - 完整的CLIENT_ID将由PayCoreSignUtil自动生成为: PAYCORE_user123
    private static final String CLIENT_ID = "user123";
    
    public static void main(String[] args) {
        System.out.println("=== PayCore HTTP请求测试 ===");
        
        // 创建签名工具 - 现在只需要传入用户ID，工具类会自动处理
        PayCoreSignUtil signUtil = new PayCoreSignUtil(CLIENT_ID, true);
        
        // 运行测试
        testCreateOrder(signUtil);
        testQueryOrder(signUtil);
    }
    
    /**
     * 测试创建订单
     */
    private static void testCreateOrder(PayCoreSignUtil signUtil) {
        System.out.println("\n=== 测试创建订单 ===");
        
        try {
            // 构建订单数据 - 使用和Go客户端完全一样的参数
            Map<String, String> orderData = new HashMap<>();
            orderData.put("app_order_no", "ORDER_" + System.currentTimeMillis());
            orderData.put("amount", "100.5");  // 使用和Go客户端一样的金额
            orderData.put("subject", "测试商品");  // 使用和Go客户端一样的标题
            orderData.put("pay_channel", "wechat");  // 使用和Go客户端一样的支付渠道
            orderData.put("app_user_id", "user_123");
            
            // 发送POST请求
            String response = sendPostRequest("/api/v1/orders", orderData, signUtil);
            System.out.println("创建订单响应: " + response);
            
        } catch (Exception e) {
            System.err.println("创建订单失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试查询订单
     */
    private static void testQueryOrder(PayCoreSignUtil signUtil) {
        System.out.println("\n=== 测试查询订单 ===");
        
        try {
            // 使用订单号查询订单
            String orderNo = "20250822223958536aba"; // 使用之前创建的订单号
            
            // 发送GET请求到 /api/v1/orders/{order_no}
            String response = sendGetRequest("/api/v1/orders/" + orderNo, null, signUtil);
            System.out.println("查询订单响应: " + response);
            
        } catch (Exception e) {
            System.err.println("查询订单失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 发送POST请求
     */
    private static String sendPostRequest(String endpoint, Map<String, String> data, PayCoreSignUtil signUtil) throws Exception {
        URL url = new URL(BASE_URL + endpoint);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        try {
            // 设置请求方法
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setDoOutput(true);
            
            // 构建JSON请求体 - 使用Map<String, Object>支持不同数据类型
            Map<String, Object> jsonMap = new HashMap<>();
            for (Map.Entry<String, String> entry : data.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                
                // 对于数字类型的字段，转换为数字
                if ("amount".equals(key) || "expire_time".equals(key)) {
                    try {
                        if (value.contains(".")) {
                            jsonMap.put(key, Double.parseDouble(value));
                        } else {
                            jsonMap.put(key, Long.parseLong(value));
                        }
                    } catch (NumberFormatException e) {
                        jsonMap.put(key, value); // 如果转换失败，保持字符串
                    }
                } else {
                    jsonMap.put(key, value);
                }
            }
            
            String jsonBody = mapToJson(jsonMap);
            System.out.println("构建的JSON: " + jsonBody);
            
            // 使用签名工具生成认证头部
            Map<String, String> authHeaders = signUtil.generateAuthHeaders(data);
            
            // 设置认证头部
            for (Map.Entry<String, String> header : authHeaders.entrySet()) {
                connection.setRequestProperty(header.getKey(), header.getValue());
            }
            
            System.out.println("POST请求: " + url);
            System.out.println("X-Timestamp: " + authHeaders.get("X-Timestamp"));
            System.out.println("X-Signature: " + authHeaders.get("X-Signature"));
            System.out.println("X-Nonce: " + authHeaders.get("X-Nonce"));
            System.out.println("X-API-Key: " + authHeaders.get("X-API-Key"));
            System.out.println("请求体: " + jsonBody);
            
            // 发送请求体
            try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
                wr.write(jsonBody.getBytes("UTF-8"));
                wr.flush();
            }
            
            // 读取响应
            int responseCode = connection.getResponseCode();
            System.out.println("响应状态码: " + responseCode);
            
            BufferedReader reader;
            if (responseCode >= 200 && responseCode < 300) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
            } else {
                reader = new BufferedReader(new InputStreamReader(connection.getErrorStream(), "UTF-8"));
            }
            
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();
            
            return response.toString();
            
        } finally {
            connection.disconnect();
        }
    }
    
    /**
     * 发送GET请求
     */
    private static String sendGetRequest(String endpoint, Map<String, String> params, PayCoreSignUtil signUtil) throws Exception {
        // 构建查询字符串
        StringBuilder queryBuilder = new StringBuilder();
        if (params != null && !params.isEmpty()) {
            queryBuilder.append("?");
            boolean first = true;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (!first) {
                    queryBuilder.append("&");
                }
                first = false;
                queryBuilder.append(URLEncoder.encode(entry.getKey(), "UTF-8"))
                          .append("=")
                          .append(URLEncoder.encode(entry.getValue(), "UTF-8"));
            }
        }
        
        URL url = new URL(BASE_URL + endpoint + queryBuilder.toString());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        try {
            // 设置请求方法
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Accept", "application/json");
            
            // 使用签名工具生成认证头部（GET请求使用查询参数）
            Map<String, String> authHeaders = signUtil.generateAuthHeaders(params != null ? params : new HashMap<>());
            
            // 设置认证头部
            for (Map.Entry<String, String> header : authHeaders.entrySet()) {
                connection.setRequestProperty(header.getKey(), header.getValue());
            }
            
            System.out.println("GET请求: " + url);
            System.out.println("X-API-Key: " + authHeaders.get("X-API-Key"));
            System.out.println("X-Timestamp: " + authHeaders.get("X-Timestamp"));
            System.out.println("X-Nonce: " + authHeaders.get("X-Nonce"));
            System.out.println("X-Signature: " + authHeaders.get("X-Signature"));
            
            // 读取响应
            int responseCode = connection.getResponseCode();
            System.out.println("响应状态码: " + responseCode);
            
            BufferedReader reader;
            if (responseCode >= 200 && responseCode < 300) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
            } else {
                reader = new BufferedReader(new InputStreamReader(connection.getErrorStream(), "UTF-8"));
            }
            
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();
            
            return response.toString();
            
        } finally {
            connection.disconnect();
        }
    }
    
    /**
     * 将Map转换为JSON字符串
     * 支持String、Number、Boolean等基本类型
     */
    private static String mapToJson(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return "{}";
        }
        
        StringBuilder json = new StringBuilder();
        json.append("{");
        
        boolean first = true;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (!first) {
                json.append(",");
            }
            first = false;
            
            // 添加键
            json.append("\"").append(escapeJson(entry.getKey())).append("\":");
            
            // 添加值
            Object value = entry.getValue();
            if (value == null) {
                json.append("null");
            } else if (value instanceof String) {
                json.append("\"").append(escapeJson((String) value)).append("\"");
            } else if (value instanceof Number || value instanceof Boolean) {
                json.append(value.toString());
            } else {
                // 其他类型转为字符串
                json.append("\"").append(escapeJson(value.toString())).append("\"");
            }
        }
        
        json.append("}");
        return json.toString();
    }
    
    /**
     * 转义JSON字符串中的特殊字符
     */
    private static String escapeJson(String str) {
        if (str == null) {
            return null;
        }
        return str.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\b", "\\b")
                  .replace("\f", "\\f")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }
}