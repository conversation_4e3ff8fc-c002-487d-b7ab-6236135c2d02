package main

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
)

// PayCoreClient 支付核心客户端
type PayCoreClient struct {
	ClientID   string
	AppSecret  string
	BaseURL    string
	HTTPClient *http.Client
}

// NewPayCoreClient 创建新的客户端
func NewPayCoreClient(clientID, appSecret, baseURL string) *PayCoreClient {
	return &PayCoreClient{
		ClientID:  clientID,
		AppSecret: appSecret,
		BaseURL:   baseURL,
		HTTPClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	AppOrderNo string  `json:"app_order_no"`
	Amount     float64 `json:"amount"`
	Subject    string  `json:"subject"`
	Body       string  `json:"body,omitempty"`
	PayChannel string  `json:"pay_channel"`
	AppUserID  string  `json:"app_user_id,omitempty"`
	ExpireTime int64   `json:"expire_time,omitempty"`
	Attach     string  `json:"attach,omitempty"`
}

// Response 通用响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// CreateOrder 创建支付订单
func (c *PayCoreClient) CreateOrder(req *CreateOrderRequest) (*Response, error) {
	return c.request("POST", "/api/v1/orders", req)
}

// GetOrder 获取订单信息
func (c *PayCoreClient) GetOrder(orderNo string) (*Response, error) {
	return c.request("GET", fmt.Sprintf("/api/v1/orders/%s", orderNo), nil)
}

// QueryOrder 查询订单
func (c *PayCoreClient) QueryOrder(params map[string]string) (*Response, error) {
	path := "/api/v1/orders/query"
	if len(params) > 0 {
		var parts []string
		for k, v := range params {
			parts = append(parts, fmt.Sprintf("%s=%s", k, v))
		}
		path += "?" + strings.Join(parts, "&")
	}
	return c.request("GET", path, nil)
}

// RefundOrder 申请退款
func (c *PayCoreClient) RefundOrder(orderNo string, refundAmount float64, refundReason string) (*Response, error) {
	req := map[string]interface{}{
		"refund_amount": refundAmount,
		"refund_reason": refundReason,
	}
	return c.request("POST", fmt.Sprintf("/api/v1/orders/%s/refund", orderNo), req)
}

// request 发送HTTP请求
func (c *PayCoreClient) request(method, path string, body interface{}) (*Response, error) {
	url := c.BaseURL + path

	// 准备请求体
	var reqBody []byte
	var err error
	if body != nil {
		reqBody, err = json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("marshal request body: %w", err)
		}
	}

	// 创建HTTP请求
	req, err := http.NewRequest(method, url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("create request: %w", err)
	}

	// 设置Content-Type
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// 生成认证头
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := c.generateNonce()
	signature := c.generateSignature(method, path, reqBody, timestamp, nonce)

	// 设置认证头
	req.Header.Set("X-API-Key", c.ClientID)
	req.Header.Set("X-Timestamp", timestamp)
	req.Header.Set("X-Nonce", nonce)
	req.Header.Set("X-Signature", signature)

	// 发送请求
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response: %w", err)
	}

	// 解析响应
	var response Response
	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("unmarshal response: %w", err)
	}

	return &response, nil
}

// generateNonce 生成随机字符串
func (c *PayCoreClient) generateNonce() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, 16)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// generateSignature 生成签名
func (c *PayCoreClient) generateSignature(method, path string, body []byte, timestamp, nonce string) string {
	params := make(map[string]string)

	// 解析URL参数
	if strings.Contains(path, "?") {
		parts := strings.Split(path, "?")
		if len(parts) > 1 {
			queryParams := strings.Split(parts[1], "&")
			for _, param := range queryParams {
				kv := strings.Split(param, "=")
				if len(kv) == 2 {
					params[kv[0]] = kv[1]
				}
			}
		}
	}

	// 解析JSON参数
	if len(body) > 0 {
		var jsonParams map[string]interface{}
		if err := json.Unmarshal(body, &jsonParams); err == nil {
			for key, value := range jsonParams {
				params[key] = fmt.Sprintf("%v", value)
			}
		}
	}

	// 添加认证参数
	params["api_key"] = c.ClientID
	params["timestamp"] = timestamp
	params["nonce"] = nonce

	// 生成签名
	return c.calculateSignature(params)
}

// calculateSignature 计算签名
func (c *PayCoreClient) calculateSignature(params map[string]string) string {
	// 1. 获取所有键并排序
	keys := make([]string, 0, len(params))
	for key := range params {
		if key != "signature" && params[key] != "" {
			keys = append(keys, key)
		}
	}
	sort.Strings(keys)

	// 2. 构建签名字符串
	var parts []string
	for _, key := range keys {
		parts = append(parts, fmt.Sprintf("%s=%s", key, params[key]))
	}

	signStr := strings.Join(parts, "&") + "&key=" + c.AppSecret

	// 3. 计算HMAC-SHA256
	h := hmac.New(sha256.New, []byte(c.AppSecret))
	h.Write([]byte(signStr))

	return hex.EncodeToString(h.Sum(nil))
}

// 使用示例
func main() {
	// 创建客户端
	client := NewPayCoreClient(
		"robot_user123",               // 客户端ID (格式: {appCode}_{appUserId})
		"7Hk9pJ2fTdR8sLq4wVbN5zKmG6xYcX3F", // 应用密钥
		"http://localhost:8080",       // 服务器地址
	)

	// 创建订单 - 测试微信支付
	orderReq := &CreateOrderRequest{
		AppOrderNo: fmt.Sprintf("ORDER_%d", time.Now().Unix()),
		Amount:     100.50,
		Subject:    "测试商品",
		Body:       "", // 不发送body字段，避免微信支付V3 API的detail字段问题
		PayChannel: "wechat",
		AppUserID:  "user_123",
	}

	resp, err := client.CreateOrder(orderReq)
	if err != nil {
		fmt.Printf("创建订单失败: %v\n", err)
		return
	}

	fmt.Printf("创建订单响应: %+v\n", resp)

	// 查询订单
	if resp.Code == 200 {
		// 假设返回的数据中包含order_no
		if data, ok := resp.Data.(map[string]interface{}); ok {
			if orderNo, ok := data["order_no"].(string); ok {
				queryResp, err := client.GetOrder(orderNo)
				if err != nil {
					fmt.Printf("查询订单失败: %v\n", err)
				} else {
					fmt.Printf("查询订单响应: %+v\n", queryResp)
				}
			}
		}
	}
}
