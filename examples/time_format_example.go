package main

import (
	"encoding/json"
	"fmt"
	"time"

	"pay-core/internal/handler"
	"pay-core/internal/model"
	"pay-core/pkg/types"
	"github.com/shopspring/decimal"
)

// 模拟API响应示例
func main() {
	fmt.Println("=== 支付系统API时间格式化示例 ===\n")

	// 1. 创建订单响应示例
	createOrderResp := &model.CreateOrderResponse{
		OrderNo:     "PAY_20250823085717001",
		TotalAmount: decimal.NewFromFloat(99.99),
		PayInfo: model.JSON{
			"qr_code": "https://qr.alipay.com/bax08888",
			"deep_link": "alipays://platformapi/startapp",
		},
		ExpireTime: types.Now().Add(30 * time.Minute),
		PayChannel: "ALIPAY",
	}

	fmt.Println("1. 创建订单响应:")
	printJSON(handler.SuccessResponse(createOrderResp))

	// 2. 查询订单响应示例
	queryOrderResp := &model.RobotOrderResponse{
		OrderNo:     "PAY_20250823085717001",
		TotalAmount: decimal.NewFromFloat(99.99),
		Status:      model.OrderStatusPaid,
		StatusText:  "支付成功",
		IsPaid:      true,
		IsExpired:   false,
		PaidAt:      &[]types.Time{types.Now().Add(-5 * time.Minute)}[0],
		ExpireTime:  types.Now().Add(25 * time.Minute),
		PayChannel:  "ALIPAY",
		Subject:     "测试商品",
	}

	fmt.Println("\n2. 查询订单响应:")
	printJSON(handler.SuccessResponse(queryOrderResp))

	// 3. 支付记录响应示例
	paymentRecord := &model.PaymentRecord{
		ID:               1,
		OrderNo:          "PAY_20250823085717001",
		OutTradeNo:       "OUT_20250823085717001",
		TradeNo:          "2025082322001234567890123456",
		PayChannel:       "ALIPAY",
		TotalAmount:      decimal.NewFromFloat(99.99),
		ReceiptAmount:    decimal.NewFromFloat(99.99),
		FeeAmount:        decimal.NewFromFloat(0.30),
		TradeTime:        types.Now().Add(-5 * time.Minute),
		ReconcileStatus:  "SUCCESS",
		SettlementStatus: "PENDING",
		CreatedAt:        types.Now().Add(-5 * time.Minute),
		UpdatedAt:        types.Now(),
	}

	fmt.Println("\n3. 支付记录响应:")
	printJSON(handler.SuccessResponse(paymentRecord))

	// 4. 对账批次响应示例
	reconcileBatch := &model.ReconciliationBatch{
		ID:            1,
		BatchNo:       "RECONCILE_20250823_ALIPAY",
		PayChannel:    "ALIPAY",
		ReconcileDate: types.NewTime(time.Date(2025, 8, 23, 0, 0, 0, 0, time.UTC)),
		Status:        "COMPLETED",
		StartedAt:     &[]types.Time{types.Now().Add(-2 * time.Hour)}[0],
		FinishedAt:    &[]types.Time{types.Now().Add(-1 * time.Hour)}[0],
		CreatedAt:     types.Now().Add(-2 * time.Hour),
		UpdatedAt:     types.Now().Add(-1 * time.Hour),
	}

	fmt.Println("\n4. 对账批次响应:")
	printJSON(handler.SuccessResponse(reconcileBatch))

	fmt.Println("\n=== 时间格式说明 ===")
	fmt.Println("所有时间字段统一使用格式: 2006-01-02 15:04:05")
	fmt.Println("- created_at: 创建时间")
	fmt.Println("- updated_at: 更新时间") 
	fmt.Println("- expire_time: 过期时间")
	fmt.Println("- paid_at: 支付时间")
	fmt.Println("- trade_time: 交易时间")
	fmt.Println("- started_at: 开始时间")
	fmt.Println("- finished_at: 完成时间")
	fmt.Println("- null值表示时间为空")
}

func printJSON(data interface{}) {
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		fmt.Printf("JSON序列化失败: %v\n", err)
		return
	}
	fmt.Println(string(jsonData))
}