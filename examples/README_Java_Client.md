# PayCore Java客户端认证工具

## 概述

PayCore Java客户端提供了纯粹的签名工具和HTTP请求测试示例，专注于认证功能的实现。

## 核心组件

### PayCoreSignUtil（签名工具类）
- **功能**: 提供纯粹的签名和参数构造功能，不包含HTTP通信
- **特性**: 
  - HMAC-SHA256签名算法
  - 防重放攻击（nonce机制）
  - 时间戳验证
  - 认证头生成
  - 签名验证
  - 调试模式

### PayCoreHttpTest（HTTP测试类）
- **功能**: 演示如何使用签名工具类发送真实的HTTP请求
- **特性**:
  - 完整的GET/POST请求示例
  - 认证头集成
  - 错误处理演示

## 快速开始

### 基本使用

```java
// 创建签名工具
PayCoreSignUtil signUtil = new PayCoreSignUtil(
    "APP_001_user123",                          // 客户端ID (格式: {appCode}_{appUserId})
    "7Hk9pJ2fTdR8sLq4wVbN5zKmG6xYcX3F",      // 应用密钥
    true                                        // 开启调试模式
);

// 生成认证头
Map<String, String> params = new HashMap<>();
params.put("app_order_no", "ORDER_123");
params.put("amount", "99.99");

Map<String, String> authHeaders = signUtil.generateAuthHeaders(params);

// 在HTTP请求中使用认证头
for (Map.Entry<String, String> header : authHeaders.entrySet()) {
    connection.setRequestProperty(header.getKey(), header.getValue());
}
```

### HTTP请求示例

```java
// 参考 PayCoreHttpTest.java 中的完整实现
PayCoreSignUtil signUtil = new PayCoreSignUtil("client_id", "secret", true);

// POST请求
String response = sendPostRequest("/api/v1/orders", orderData, signUtil);

// GET请求
String response = sendGetRequest("/api/v1/orders/query", queryParams, signUtil);
```

## API方法

### PayCoreSignUtil

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `generateAuthHeaders(params)` | 生成认证头 | params: 请求参数 | Map<String, String> |
| `generateSignature(params, timestamp, nonce)` | 生成签名 | 签名参数 | String签名 |
| `verifySignature(...)` | 验证签名 | 签名参数 | boolean |
| `generateNonce()` | 生成随机数 | 无 | String |
| `generateTimestamp()` | 生成时间戳 | 无 | String |
| `buildSignParams(params)` | 构造签名参数 | params: 原始参数 | Map<String, String> |
| `hmacSha256(data, key)` | HMAC-SHA256计算 | 数据和密钥 | String |

## 认证机制

### 签名算法
1. 收集所有请求参数（包括查询参数和请求体参数）
2. 添加认证参数：`api_key`, `timestamp`, `nonce`
3. 按参数名排序
4. 构建签名字符串：`key1=value1&key2=value2&...&key=app_secret`
5. 使用HMAC-SHA256计算签名

### 请求头
- `X-API-Key`: 客户端ID
- `X-Timestamp`: Unix时间戳
- `X-Nonce`: 随机数（防重放）
- `X-Signature`: HMAC-SHA256签名

## 客户端ID格式

客户端ID采用 `{client_type}_{identifier}` 格式：

- `app_android_001`: Android应用
- `app_ios_001`: iOS应用  
- `web_admin`: Web管理后台
- `robot_001`: 机器人客户端
- `mini_program_001`: 小程序

## 错误处理

```java
try {
    Map<String, String> headers = signUtil.generateAuthHeaders(params);
    // 使用认证头发送HTTP请求
} catch (Exception e) {
    System.err.println("签名生成失败: " + e.getMessage());
    e.printStackTrace();
}
```

## 调试模式

```java
// 开启调试模式
PayCoreSignUtil signUtil = new PayCoreSignUtil(
    "app_android_001",
    "your_secret",
    true  // 开启调试模式
);
```

调试模式会输出：
- 签名字符串
- 认证参数信息

## 线程安全

`PayCoreSignUtil`是线程安全的，可以在多线程环境中安全使用。

## 文件说明

### PayCoreSignUtil.java
纯粹的签名工具类，专注于：
- 签名生成和验证
- 认证参数构造
- 工具方法提供
- 不包含任何HTTP通信功能

### PayCoreHttpTest.java
HTTP请求测试示例，展示：
- 如何使用签名工具类
- 完整的HTTP请求流程
- GET和POST请求示例
- 错误处理和调试输出

## 集成指南

### 1. 复制工具类
将 `PayCoreSignUtil.java` 复制到你的项目中

### 2. 创建实例
```java
PayCoreSignUtil signUtil = new PayCoreSignUtil("your_client_id", "your_secret");
```

### 3. 生成认证头
```java
Map<String, String> authHeaders = signUtil.generateAuthHeaders(requestParams);
```

### 4. 设置HTTP请求头
```java
for (Map.Entry<String, String> header : authHeaders.entrySet()) {
    // 根据你使用的HTTP客户端设置请求头
    httpRequest.setHeader(header.getKey(), header.getValue());
}
```

## 注意事项

1. **密钥安全**: 不要在代码中硬编码密钥，建议从配置文件或环境变量读取
2. **时间同步**: 确保客户端时间与服务器时间同步（误差不超过5分钟）
3. **参数完整**: 确保所有请求参数都参与签名计算
4. **编码一致**: 使用UTF-8编码进行签名计算

## 配置示例

```java
// 生产环境配置
PayCoreSignUtil prodSignUtil = new PayCoreSignUtil(
    System.getenv("PAY_CORE_CLIENT_ID"),
    System.getenv("PAY_CORE_APP_SECRET")
);

// 测试环境配置
PayCoreSignUtil testSignUtil = new PayCoreSignUtil(
    "app_test_001",
    "test_secret_key",
    true  // 测试环境开启调试
);
```

## 测试运行

```bash
# 编译
javac PayCoreSignUtil.java PayCoreHttpTest.java

# 运行测试（需要服务器运行在localhost:8080）
java PayCoreHttpTest
```

测试将验证：
- 创建订单请求
- 查询订单请求  
- 无效签名处理