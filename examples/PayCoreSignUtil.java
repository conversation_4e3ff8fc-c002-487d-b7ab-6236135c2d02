import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

/**
 * PayCore签名工具类
 * 
 * 功能：
 * - 生成HMAC-SHA256签名
 * - 生成认证头
 * - 支持GET和POST请求签名
 */
public class PayCoreSignUtil {
    private final static String APP_CODE = "robot";
    private final static String APP_SECRET = "7Hk9pJ2fTdR8sLq4wVbN5zKmG6xYcX3F";
    private final String clientId;
    private final String appSecret;
    private final boolean debugMode;
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID，将自动拼接为完整的CLIENT_ID格式：PAYCORE_userId
     */
    public PayCoreSignUtil(String userId) {
        this(userId, false);
    }
    
    /**
     * 构造函数（带调试模式）
     * 
     * @param userId 用户ID，将自动拼接为完整的CLIENT_ID格式：PAYCORE_userId
     * @param debugMode 是否开启调试模式
     */
    public PayCoreSignUtil(String userId, boolean debugMode) {
        this.clientId = APP_CODE + "_" + userId;
        this.appSecret = APP_SECRET;
        this.debugMode = debugMode;
    }
    
    /**
     * 生成认证头（用于GET请求或简单POST请求）
     * 
     * @param params 请求参数（可以为null）
     * @return 认证头Map，包含X-API-Key, X-Timestamp, X-Nonce, X-Signature
     * @throws Exception 签名生成异常
     */
    public Map<String, String> generateAuthHeaders(Map<String, String> params) throws Exception {
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String nonce = generateNonce();
        String signature = generateSignature(params, timestamp, nonce);
        
        Map<String, String> headers = new HashMap<>();
        headers.put("X-API-Key", clientId);
        headers.put("X-Timestamp", timestamp);
        headers.put("X-Nonce", nonce);
        headers.put("X-Signature", signature);
        
        return headers;
    }
    

    /**
     * 生成签名
     * 
     * @param params 请求参数
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @return HMAC-SHA256签名
     * @throws Exception 签名生成异常
     */
    private String generateSignature(Map<String, String> params, String timestamp, String nonce) throws Exception {
        // 复制参数并添加认证参数
        Map<String, String> allParams = new HashMap<>();
        if (params != null) {
            allParams.putAll(params);
        }
        allParams.put("api_key", clientId);
        allParams.put("timestamp", timestamp);
        allParams.put("nonce", nonce);
        
        // 按键名排序
        List<String> keys = new ArrayList<>(allParams.keySet());
        Collections.sort(keys);
        
        // 构建签名字符串
        List<String> parts = new ArrayList<>();
        for (String key : keys) {
            String value = allParams.get(key);
            if (value != null && !value.isEmpty() && !"signature".equals(key)) {
                parts.add(key + "=" + value);
            }
        }
        
        String signString = String.join("&", parts) + "&key=" + appSecret;
        
        if (debugMode) {
            System.out.println("签名字符串: " + signString);
        }
        
        // 计算HMAC-SHA256
        return hmacSha256(signString, appSecret);
    }
    
    /**
     * 生成随机nonce
     * 
     * @return 随机nonce字符串
     */
    private String generateNonce() {
        return "robot_" + System.nanoTime();
    }
    
    /**
     * 计算HMAC-SHA256
     * 
     * @param data 待签名数据
     * @param key 签名密钥
     * @return 十六进制签名字符串
     * @throws Exception 签名计算异常
     */
    private String hmacSha256(String data, String key) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        mac.init(secretKeySpec);
        
        byte[] hash = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        
        // 转换为十六进制字符串
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        
        return hexString.toString();
    }
    
    // Getter方法
    public String getClientId() {
        return clientId;
    }
}