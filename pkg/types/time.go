package types

import (
	"database/sql/driver"
	"fmt"
	"strings"
	"time"
)

const TimeFormat = "2006-01-02 15:04:05"

// Time 自定义时间类型，统一格式化为 "2024-01-01 12:00:00" 格式
type Time struct {
	time.Time
}

// NewTime 创建新的时间类型
func NewTime(t time.Time) Time {
	return Time{Time: t}
}

// Now 获取当前时间
func Now() Time {
	return Time{Time: time.Now()}
}

// MarshalJSON 实现 JSON 序列化
func (t Time) MarshalJSON() ([]byte, error) {
	if t.Time.IsZero() {
		return []byte("null"), nil
	}
	return []byte(fmt.Sprintf(`"%s"`, t.Time.Format(TimeFormat))), nil
}

// UnmarshalJSON 实现 JSON 反序列化
func (t *Time) UnmarshalJSON(data []byte) error {
	str := strings.Trim(string(data), `"`)
	if str == "null" || str == "" {
		t.Time = time.Time{}
		return nil
	}
	
	parsed, err := time.Parse(TimeFormat, str)
	if err != nil {
		return err
	}
	t.Time = parsed
	return nil
}

// Value 实现 driver.Valuer 接口，用于数据库存储
func (t Time) Value() (driver.Value, error) {
	if t.Time.IsZero() {
		return nil, nil
	}
	return t.Time, nil
}

// Scan 实现 sql.Scanner 接口，用于数据库读取
func (t *Time) Scan(value interface{}) error {
	if value == nil {
		t.Time = time.Time{}
		return nil
	}
	
	switch v := value.(type) {
	case time.Time:
		t.Time = v
	case string:
		parsed, err := time.Parse(TimeFormat, v)
		if err != nil {
			return err
		}
		t.Time = parsed
	default:
		return fmt.Errorf("cannot scan %T into Time", value)
	}
	return nil
}

// String 返回格式化的时间字符串
func (t Time) String() string {
	if t.Time.IsZero() {
		return ""
	}
	return t.Time.Format(TimeFormat)
}

// IsZero 检查时间是否为零值
func (t Time) IsZero() bool {
	return t.Time.IsZero()
}

// After 检查时间是否在指定时间之后
func (t Time) After(u Time) bool {
	return t.Time.After(u.Time)
}

// Before 检查时间是否在指定时间之前
func (t Time) Before(u Time) bool {
	return t.Time.Before(u.Time)
}

// Add 添加时间间隔
func (t Time) Add(d time.Duration) Time {
	return Time{Time: t.Time.Add(d)}
}

// Sub 计算时间差
func (t Time) Sub(u Time) time.Duration {
	return t.Time.Sub(u.Time)
}

// Unix 返回时间戳
func (t Time) Unix() int64 {
	return t.Time.Unix()
}

// Format 格式化时间
func (t Time) Format(layout string) string {
	return t.Time.Format(layout)
}