package payment

import (
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"pay-core/internal/config"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestNewClient 测试创建支付客户端
func TestNewClient(t *testing.T) {
	tests := []struct {
		name   string
		config config.PaymentConfig
		hasErr bool
	}{
		{
			name: "valid alipay config",
			config: config.PaymentConfig{
				Alipay: config.AlipayConfig{
					AppID:      "test_app_id",
					PrivateKey: "test_private_key",
					PublicKey:  "test_public_key",
					IsProd:     false,
				},
			},
			hasErr: false,
		},
		{
			name: "valid wechat config",
			config: config.PaymentConfig{
				Wechat: config.WechatConfig{
					AppID:  "test_app_id",
					MchID:  "test_mch_id",
					APIKey: "test_api_key",
					IsProd: false,
				},
			},
			hasErr: false,
		},
		{
			name: "empty config",
			config: config.PaymentConfig{},
			hasErr: false, // 空配置不应该报错，只是客户端为nil
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client, err := NewClient(tt.config)
			
			if tt.hasErr {
				assert.Error(t, err)
				assert.Nil(t, client)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, client)
			}
		})
	}
}

// TestClient_CreateNativeOrder 测试创建Native支付订单
func TestClient_CreateNativeOrder(t *testing.T) {
	// 注意：这个测试需要真实的支付配置才能运行
	// 在实际环境中，应该使用沙箱环境进行测试
	t.Skip("Skipping integration test - requires real payment credentials")

	cfg := config.PaymentConfig{
		Alipay: config.AlipayConfig{
			AppID:      "your_sandbox_app_id",
			PrivateKey: "your_sandbox_private_key",
			PublicKey:  "your_sandbox_public_key",
			IsProd:     false,
		},
	}

	client, err := NewClient(cfg)
	require.NoError(t, err)

	req := &NativeOrderRequest{
		OrderNo:       "test_order_001",
		Subject:       "Test Product",
		Body:          "Test Product Description",
		Amount:        decimal.NewFromFloat(0.01), // 测试金额1分
		Channel:       "alipay",
		NotifyURL:     "http://example.com/notify",
		ExpireMinutes: 30,
	}

	resp, err := client.CreateNativeOrder(req)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.NotEmpty(t, resp.QRCode)
	assert.Equal(t, req.OrderNo, resp.OrderNo)
}

// TestClient_CreateNativeOrder_UnsupportedChannel 测试不支持的支付渠道
func TestClient_CreateNativeOrder_UnsupportedChannel(t *testing.T) {
	client := &Client{}

	req := &NativeOrderRequest{
		OrderNo: "test_order_001",
		Channel: "unsupported_channel",
	}

	resp, err := client.CreateNativeOrder(req)
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "unsupported payment channel")
}

// TestClient_QueryOrder_UnsupportedChannel 测试查询不支持的支付渠道
func TestClient_QueryOrder_UnsupportedChannel(t *testing.T) {
	client := &Client{}

	req := &QueryOrderRequest{
		OrderNo: "test_order_001",
		Channel: "unsupported_channel",
	}

	resp, err := client.QueryOrder(req)
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "unsupported payment channel")
}

// TestClient_ParseNotify_UnsupportedChannel 测试解析不支持的支付渠道回调
func TestClient_ParseNotify_UnsupportedChannel(t *testing.T) {
	client := &Client{}
	ctx := context.Background()

	req := httptest.NewRequest("POST", "/notify", strings.NewReader("test_body"))

	resp, err := client.ParseNotify(ctx, "unsupported_channel", req)
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "unsupported payment channel")
}

// TestClient_createAlipayNativeOrder_ClientNotInitialized 测试支付宝客户端未初始化
func TestClient_createAlipayNativeOrder_ClientNotInitialized(t *testing.T) {
	client := &Client{} // 没有初始化支付宝客户端

	req := &NativeOrderRequest{
		OrderNo: "test_order_001",
		Channel: "alipay",
	}

	resp, err := client.createAlipayNativeOrder(req)
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "alipay client not initialized")
}

// TestClient_createWechatNativeOrder_ClientNotInitialized 测试微信客户端未初始化
func TestClient_createWechatNativeOrder_ClientNotInitialized(t *testing.T) {
	client := &Client{} // 没有初始化微信客户端

	req := &NativeOrderRequest{
		OrderNo: "test_order_001",
		Channel: "wechat",
	}

	resp, err := client.createWechatNativeOrder(req)
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "wechat client not initialized")
}

// TestClient_queryAlipayOrder_ClientNotInitialized 测试查询支付宝订单客户端未初始化
func TestClient_queryAlipayOrder_ClientNotInitialized(t *testing.T) {
	client := &Client{} // 没有初始化支付宝客户端

	req := &QueryOrderRequest{
		OrderNo: "test_order_001",
		Channel: "alipay",
	}

	resp, err := client.queryAlipayOrder(req)
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "alipay client not initialized")
}

// TestClient_queryWechatOrder_ClientNotInitialized 测试查询微信订单客户端未初始化
func TestClient_queryWechatOrder_ClientNotInitialized(t *testing.T) {
	client := &Client{} // 没有初始化微信客户端

	req := &QueryOrderRequest{
		OrderNo: "test_order_001",
		Channel: "wechat",
	}

	resp, err := client.queryWechatOrder(req)
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "wechat client not initialized")
}

// TestClient_parseAlipayNotify_ClientNotInitialized 测试解析支付宝回调客户端未初始化
func TestClient_parseAlipayNotify_ClientNotInitialized(t *testing.T) {
	client := &Client{} // 没有初始化支付宝客户端
	ctx := context.Background()

	req := httptest.NewRequest("POST", "/notify", strings.NewReader("test_body"))

	resp, err := client.parseAlipayNotify(ctx, req)
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "alipay client not initialized")
}

// TestClient_parseWechatNotify_ClientNotInitialized 测试解析微信回调客户端未初始化
func TestClient_parseWechatNotify_ClientNotInitialized(t *testing.T) {
	client := &Client{} // 没有初始化微信客户端
	ctx := context.Background()

	req := httptest.NewRequest("POST", "/notify", strings.NewReader("test_body"))

	resp, err := client.parseWechatNotify(ctx, req)
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "wechat client not initialized")
}

// TestGenerateRandomString 测试生成随机字符串
func TestGenerateRandomString(t *testing.T) {
	tests := []struct {
		name   string
		length int
	}{
		{"length 8", 8},
		{"length 16", 16},
		{"length 32", 32},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := generateRandomString(tt.length)
			assert.Equal(t, tt.length, len(result))
			
			// 验证是否为十六进制字符串
			for _, char := range result {
				assert.True(t, (char >= '0' && char <= '9') || (char >= 'a' && char <= 'f'))
			}
		})
	}
}

// TestNotifyType_Constants 测试通知类型常量
func TestNotifyType_Constants(t *testing.T) {
	assert.Equal(t, "paid", NotifyTypePaid)
	assert.Equal(t, "refunded", NotifyTypeRefunded)
}

// BenchmarkGenerateRandomString 基准测试生成随机字符串
func BenchmarkGenerateRandomString(b *testing.B) {
	for i := 0; i < b.N; i++ {
		generateRandomString(32)
	}
}
