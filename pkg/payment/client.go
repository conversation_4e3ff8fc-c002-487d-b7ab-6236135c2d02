package payment

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"net/http"
	"os"
	"time"

	"pay-core/internal/config"
	"pay-core/pkg/logger"

	"github.com/go-pay/gopay"
	wechatv3 "github.com/go-pay/gopay/wechat/v3"
	"github.com/shopspring/decimal"
)

// Client 支付客户端
type Client struct {
	wechatV3Client *wechatv3.ClientV3
	config         config.PaymentConfig
}

// NewClient 创建支付客户端
func NewClient(cfg config.PaymentConfig) (*Client, error) {
	client := &Client{
		config: cfg,
	}

	// 初始化微信支付V3客户端
	if cfg.Wechat.AppID != "" && cfg.Wechat.MchID != "" && cfg.Wechat.APIv3Key != "" {
		// 获取商户私钥内容
		var privateKeyContent string
		if cfg.Wechat.PrivateKey != "" {
			privateKeyContent = cfg.Wechat.PrivateKey
		} else if cfg.Wechat.PrivateKeyPath != "" {
			// 从文件读取私钥
			keyBytes, err := os.ReadFile(cfg.Wechat.PrivateKeyPath)
			if err != nil {
				return nil, fmt.Errorf("failed to read wechat private key file: %w", err)
			}
			privateKeyContent = string(keyBytes)
		} else {
			return nil, fmt.Errorf("wechat private key is required")
		}

		// 创建微信支付V3客户端
		wechatV3Client, err := wechatv3.NewClientV3(cfg.Wechat.MchID, cfg.Wechat.SerialNo, cfg.Wechat.APIv3Key, privateKeyContent)
		if err != nil {
			return nil, fmt.Errorf("failed to create wechat V3 client: %w", err)
		}

		// 设置微信支付平台证书验签
		if cfg.Wechat.PlatformCertPath != "" {
			// 使用配置文件中的平台证书
			certBytes, err := os.ReadFile(cfg.Wechat.PlatformCertPath)
			if err != nil {
				logger.WithError(err).Warn("Failed to read platform cert file, trying auto verify")
				// 如果读取本地证书失败，尝试自动验签
				err = wechatV3Client.AutoVerifySign()
				if err != nil {
					return nil, fmt.Errorf("failed to setup wechat verify sign: %w", err)
				}
			} else {
				// 使用本地证书设置验签
				err = wechatV3Client.SetPlatformCert(certBytes, cfg.Wechat.PlatformCertSerialNo)
				if err != nil {
					logger.WithError(err).Warn("Failed to set platform cert, trying auto verify")
					// 如果设置本地证书失败，尝试自动验签
					err = wechatV3Client.AutoVerifySign()
					if err != nil {
						return nil, fmt.Errorf("failed to setup wechat verify sign: %w", err)
					}
				} else {
					logger.Info("Wechat V3 client initialized with local platform certificate")
				}
			}
		} else if cfg.Wechat.PlatformCert != "" {
			// 使用配置文件中的平台证书内容
			err = wechatV3Client.SetPlatformCert([]byte(cfg.Wechat.PlatformCert), cfg.Wechat.PlatformCertSerialNo)
			if err != nil {
				logger.WithError(err).Warn("Failed to set platform cert content, trying auto verify")
				err = wechatV3Client.AutoVerifySign()
				if err != nil {
					return nil, fmt.Errorf("failed to setup wechat verify sign: %w", err)
				}
			} else {
				logger.Info("Wechat V3 client initialized with platform certificate content")
			}
		} else {
			// 没有配置平台证书，使用自动验签
			err = wechatV3Client.AutoVerifySign()
			if err != nil {
				return nil, fmt.Errorf("failed to setup wechat auto verify sign: %w", err)
			}
			logger.Info("Wechat V3 client initialized with auto verification")
		}

		client.wechatV3Client = wechatV3Client
		logger.Info("Wechat V3 payment client initialized successfully")
	}

	return client, nil
}

// generateRandomString 生成随机字符串
func generateRandomString(length int) string {
	bytes := make([]byte, length/2)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// CloseOrderRequest 关闭订单请求
type CloseOrderRequest struct {
	OrderNo string `json:"order_no"`
	Channel string `json:"channel"`
}

// CloseOrderResponse 关闭订单响应
type CloseOrderResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// CloseOrder 关闭订单
func (c *Client) CloseOrder(req *CloseOrderRequest) (*CloseOrderResponse, error) {
	if req.Channel != "wechat" {
		return nil, fmt.Errorf("unsupported payment channel: %s", req.Channel)
	}
	return c.closeWechatOrder(req.OrderNo)
}

// closeWechatOrder 关闭微信订单
func (c *Client) closeWechatOrder(orderNo string) (*CloseOrderResponse, error) {
	if c.wechatV3Client == nil {
		return nil, errors.New("wechat client not initialized")
	}

	// 微信支付关闭订单 - V3接口只需要订单号
	wxRsp, err := c.wechatV3Client.V3TransactionCloseOrder(context.Background(), orderNo)
	if err != nil {
		logger.WithError(err).WithField("order_no", orderNo).Error("Failed to close wechat order")
		return &CloseOrderResponse{
			Success: false,
			Message: fmt.Sprintf("关闭微信订单失败: %v", err),
		}, err
	}

	// gopay 中 status=0 代表成功
	if wxRsp.Code == 0 || wxRsp.Code == http.StatusNoContent {
		return &CloseOrderResponse{
			Success: true,
			Message: "微信订单关闭成功",
		}, nil
	}

	return &CloseOrderResponse{
		Success: false,
		Message: fmt.Sprintf("微信订单关闭失败，状态码: %d", wxRsp.Code),
	}, fmt.Errorf("wechat close order failed with status: %d", wxRsp.Code)
}

// NativeOrderRequest Native支付订单请求
type NativeOrderRequest struct {
	OrderNo       string          `json:"order_no"`
	Subject       string          `json:"subject"`
	Body          string          `json:"body"`
	Amount        decimal.Decimal `json:"amount"`
	Channel       string          `json:"channel"`
	NotifyURL     string          `json:"notify_url"`
	ExpireMinutes int             `json:"expire_minutes"`
}

// NativeOrderResponse Native支付订单响应
type NativeOrderResponse struct {
	QRCode   string `json:"qr_code"`
	PrepayID string `json:"prepay_id"`
	OrderNo  string `json:"order_no"`
	TradeNo  string `json:"trade_no"`
}

// CreateNativeOrder 创建Native支付订单
func (c *Client) CreateNativeOrder(req *NativeOrderRequest) (*NativeOrderResponse, error) {
	startTime := time.Now()

	logger.WithFields(map[string]interface{}{
		"order_no": req.OrderNo,
		"channel":  req.Channel,
		"amount":   req.Amount,
		"subject":  req.Subject,
	}).Info("Creating native payment order")

	if req.Channel != "wechat" {
		return nil, fmt.Errorf("unsupported payment channel: %s", req.Channel)
	}

	resp, err := c.createWechatNativeOrder(req)

	duration := time.Since(startTime)

	if err != nil {
		logger.WithFields(map[string]interface{}{
			"order_no": req.OrderNo,
			"channel":  req.Channel,
			"duration": duration.Milliseconds(),
			"error":    err.Error(),
		}).Error("Failed to create native payment order")
		return nil, err
	}

	logger.WithFields(map[string]interface{}{
		"order_no": req.OrderNo,
		"channel":  req.Channel,
		"duration": duration.Milliseconds(),
		"has_qr":   resp.QRCode != "",
	}).Info("Successfully created native payment order")

	return resp, nil
}

// createWechatNativeOrder 创建微信Native支付订单（V3 API）
func (c *Client) createWechatNativeOrder(req *NativeOrderRequest) (*NativeOrderResponse, error) {
	if c.wechatV3Client == nil {
		return nil, errors.New("wechat V3 client not initialized")
	}

	// 构建V3 API请求参数
	bm := make(gopay.BodyMap)
	bm.Set("appid", c.config.Wechat.AppID).
		Set("mchid", c.config.Wechat.MchID).
		Set("description", req.Subject).
		Set("out_trade_no", req.OrderNo).
		Set("notify_url", req.NotifyURL).
		SetBodyMap("amount", func(bm gopay.BodyMap) {
			bm.Set("total", req.Amount.Mul(decimal.NewFromInt(100)).IntPart()). // 转换为分
												Set("currency", "CNY")
		})

	// 设置商品详情（暂时跳过detail字段，避免格式问题）
	// 微信V3 API的detail字段格式要求很严格，暂时不使用
	// if req.Body != "" {
	// 	bm.Set("detail", req.Body)
	// }

	// 设置过期时间（V3 API使用RFC3339格式）
	if req.ExpireMinutes > 0 {
		expireTime := time.Now().Add(time.Duration(req.ExpireMinutes) * time.Minute)
		bm.Set("time_expire", expireTime.Format(time.RFC3339))
	}

	// 调用微信支付V3 Native下单API
	ctx := context.Background()
	wxRsp, err := c.wechatV3Client.V3TransactionNative(ctx, bm)
	if err != nil {
		return nil, fmt.Errorf("wechat V3 native order failed: %w", err)
	}

	// 检查返回结果
	// 注意：gopay库可能不会正确设置Code字段，我们需要检查实际的响应内容
	if wxRsp.Error != "" {
		return nil, fmt.Errorf("wechat V3 error: %s", wxRsp.Error)
	}

	// 解析响应
	if wxRsp.Response == nil || wxRsp.Response.CodeUrl == "" {
		return nil, errors.New("wechat V3 response missing code_url")
	}

	return &NativeOrderResponse{
		QRCode:   wxRsp.Response.CodeUrl,
		PrepayID: "", // V3 API不返回prepay_id
		OrderNo:  req.OrderNo,
		TradeNo:  req.OrderNo, // 使用商户订单号作为交易号
	}, nil
}

// QueryOrderRequest 查询订单请求
type QueryOrderRequest struct {
	OrderNo       string `json:"order_no"`
	TransactionID string `json:"transaction_id"`
	Channel       string `json:"channel"`
}

// QueryOrderResponse 查询订单响应
type QueryOrderResponse struct {
	OrderNo       string          `json:"order_no"`
	TransactionID string          `json:"transaction_id"`
	Amount        decimal.Decimal `json:"amount"`
	Status        string          `json:"status"`
	PaidAt        string          `json:"paid_at"`
}

// QueryOrder 查询订单状态
func (c *Client) QueryOrder(req *QueryOrderRequest) (*QueryOrderResponse, error) {
	startTime := time.Now()

	logger.WithFields(map[string]interface{}{
		"order_no":       req.OrderNo,
		"transaction_id": req.TransactionID,
		"channel":        req.Channel,
	}).Info("Querying payment order status")

	if req.Channel != "wechat" {
		return nil, fmt.Errorf("unsupported payment channel: %s", req.Channel)
	}

	resp, err := c.queryWechatOrder(req)

	duration := time.Since(startTime)

	if err != nil {
		logger.WithFields(map[string]interface{}{
			"order_no":       req.OrderNo,
			"transaction_id": req.TransactionID,
			"channel":        req.Channel,
			"duration":       duration.Milliseconds(),
			"error":          err.Error(),
		}).Error("Failed to query payment order status")
		return nil, err
	}

	logger.WithFields(map[string]interface{}{
		"order_no":       req.OrderNo,
		"transaction_id": req.TransactionID,
		"channel":        req.Channel,
		"duration":       duration.Milliseconds(),
		"status":         resp.Status,
		"amount":         resp.Amount,
	}).Info("Successfully queried payment order status")

	return resp, nil
}

// queryWechatOrder 查询微信订单（V3 API）
func (c *Client) queryWechatOrder(req *QueryOrderRequest) (*QueryOrderResponse, error) {
	if c.wechatV3Client == nil {
		return nil, errors.New("wechat V3 client not initialized")
	}

	// V3 API查询订单
	ctx := context.Background()
	var wxRsp *wechatv3.QueryOrderRsp
	var err error

	if req.OrderNo != "" {
		// 通过商户订单号查询
		wxRsp, err = c.wechatV3Client.V3TransactionQueryOrder(ctx, wechatv3.OutTradeNo, req.OrderNo)
	} else if req.TransactionID != "" {
		// 通过微信支付订单号查询
		wxRsp, err = c.wechatV3Client.V3TransactionQueryOrder(ctx, wechatv3.TransactionId, req.TransactionID)
	} else {
		return nil, errors.New("order_no or transaction_id is required")
	}

	if err != nil {
		return nil, fmt.Errorf("wechat V3 query order failed: %w", err)
	}

	if wxRsp.Code != 200 {
		return nil, fmt.Errorf("wechat V3 query order failed: %s", wxRsp.Error)
	}

	// 解析金额
	amount := decimal.NewFromInt(int64(wxRsp.Response.Amount.Total)).Div(decimal.NewFromInt(100))

	return &QueryOrderResponse{
		OrderNo:       wxRsp.Response.OutTradeNo,
		TransactionID: wxRsp.Response.TransactionId,
		Amount:        amount,
		Status:        wxRsp.Response.TradeState,
		PaidAt:        wxRsp.Response.SuccessTime,
	}, nil
}

// Refund 申请退款
func (c *Client) Refund(req *RefundRequest) (*RefundResponse, error) {
	startTime := time.Now()

	logger.WithFields(map[string]interface{}{
		"order_no":      req.OrderNo,
		"refund_no":     req.RefundNo,
		"channel":       req.Channel,
		"refund_amount": req.RefundAmount,
	}).Info("Creating refund request")

	if req.Channel != "wechat" {
		return nil, fmt.Errorf("unsupported payment channel: %s", req.Channel)
	}

	resp, err := c.refundWechatOrder(req)

	duration := time.Since(startTime)

	if err != nil {
		logger.WithFields(map[string]interface{}{
			"order_no":  req.OrderNo,
			"refund_no": req.RefundNo,
			"channel":   req.Channel,
			"duration":  duration.Milliseconds(),
			"error":     err.Error(),
		}).Error("Failed to create refund request")
		return nil, err
	}

	logger.WithFields(map[string]interface{}{
		"order_no":      req.OrderNo,
		"refund_no":     req.RefundNo,
		"channel":       req.Channel,
		"duration":      duration.Milliseconds(),
		"out_refund_no": resp.OutRefundNo,
		"status":        resp.Status,
	}).Info("Successfully created refund request")

	return resp, nil
}

// refundWechatOrder 微信退款（V3 API）
func (c *Client) refundWechatOrder(req *RefundRequest) (*RefundResponse, error) {
	if c.wechatV3Client == nil {
		return nil, errors.New("wechat V3 client not initialized")
	}

	// 构建V3 API退款请求参数
	bm := make(gopay.BodyMap)
	bm.Set("out_trade_no", req.OrderNo).
		Set("out_refund_no", req.RefundNo).
		SetBodyMap("amount", func(bm gopay.BodyMap) {
			bm.Set("refund", req.RefundAmount.Mul(decimal.NewFromInt(100)).IntPart()). // 转换为分
													Set("total", req.TotalAmount.Mul(decimal.NewFromInt(100)).IntPart()). // 转换为分
													Set("currency", "CNY")
		})

	// 设置退款原因
	if req.RefundReason != "" {
		bm.Set("reason", req.RefundReason)
	}

	// 设置退款回调地址
	if req.NotifyURL != "" {
		bm.Set("notify_url", req.NotifyURL)
	}

	// 调用微信支付V3退款API
	ctx := context.Background()
	wxRsp, err := c.wechatV3Client.V3Refund(ctx, bm)
	if err != nil {
		return nil, fmt.Errorf("wechat V3 refund failed: %w", err)
	}

	// 检查返回结果
	if wxRsp.Error != "" {
		return nil, fmt.Errorf("wechat V3 refund error: %s", wxRsp.Error)
	}

	if wxRsp.Response == nil {
		return nil, errors.New("wechat V3 refund response is nil")
	}

	// 解析退款金额
	refundAmount := decimal.NewFromInt(int64(wxRsp.Response.Amount.Refund)).Div(decimal.NewFromInt(100))

	return &RefundResponse{
		RefundNo:     req.RefundNo,
		OutRefundNo:  wxRsp.Response.RefundId,
		RefundAmount: refundAmount,
		Status:       wxRsp.Response.Status,
		RefundTime:   wxRsp.Response.CreateTime,
	}, nil
}

// NotifyType 通知类型
type NotifyType string

const (
	NotifyTypePaid     NotifyType = "paid"
	NotifyTypeRefunded NotifyType = "refunded"
)

// NotifyData 通知数据
type NotifyData struct {
	Type          NotifyType
	OrderNo       string
	TransactionID string
	RefundNo      string // 退款单号（退款回调时使用）
	Amount        decimal.Decimal
	Status        string
	PaidAt        string
	RefundedAt    string // 退款时间（退款回调时使用）
}

// RefundRequest 退款请求
type RefundRequest struct {
	OrderNo       string          `json:"order_no"`
	RefundNo      string          `json:"refund_no"`
	TransactionID string          `json:"transaction_id"`
	RefundAmount  decimal.Decimal `json:"refund_amount"`
	TotalAmount   decimal.Decimal `json:"total_amount"`
	RefundReason  string          `json:"refund_reason"`
	NotifyURL     string          `json:"notify_url"`
	Channel       string          `json:"channel"`
}

// RefundResponse 退款响应
type RefundResponse struct {
	RefundNo     string          `json:"refund_no"`
	OutRefundNo  string          `json:"out_refund_no"`
	RefundAmount decimal.Decimal `json:"refund_amount"`
	Status       string          `json:"status"`
	RefundTime   string          `json:"refund_time"`
}

// ParseNotify 解析回调通知
func (c *Client) ParseNotify(ctx context.Context, channel string, req *http.Request) (*NotifyData, error) {
	startTime := time.Now()

	logger.WithFields(map[string]interface{}{
		"channel":     channel,
		"method":      req.Method,
		"url":         req.URL.String(),
		"remote_addr": req.RemoteAddr,
		"user_agent":  req.UserAgent(),
	}).Info("Parsing payment notification")

	if channel != "wechat" {
		return nil, fmt.Errorf("unsupported payment channel: %s", channel)
	}

	notifyData, err := c.parseWechatNotify(ctx, req)

	duration := time.Since(startTime)

	if err != nil {
		logger.WithFields(map[string]interface{}{
			"channel":  channel,
			"duration": duration.Milliseconds(),
			"error":    err.Error(),
		}).Error("Failed to parse payment notification")
		return nil, err
	}

	logger.WithFields(map[string]interface{}{
		"channel":        channel,
		"duration":       duration.Milliseconds(),
		"order_no":       notifyData.OrderNo,
		"transaction_id": notifyData.TransactionID,
		"amount":         notifyData.Amount,
		"status":         notifyData.Status,
		"type":           notifyData.Type,
	}).Info("Successfully parsed payment notification")

	return notifyData, nil
}

// parseWechatNotify 解析微信V3回调通知
func (c *Client) parseWechatNotify(ctx context.Context, req *http.Request) (*NotifyData, error) {
	if c.wechatV3Client == nil {
		return nil, errors.New("wechat V3 client not initialized")
	}

	// 解析V3回调数据
	notifyReq, err := wechatv3.V3ParseNotify(req)
	if err != nil {
		return nil, fmt.Errorf("failed to parse wechat V3 notification: %w", err)
	}

	// V3 API会自动验证签名，如果解析成功说明签名验证通过
	logger.WithField("event_type", notifyReq.EventType).Info("Wechat V3 notification parsed and verified successfully")

	// V3 API的回调数据结构不同，需要解密resource字段
	if notifyReq.Resource == nil {
		return nil, errors.New("missing resource in wechat V3 notification")
	}

	// 根据事件类型选择不同的解密方法和处理逻辑
	switch notifyReq.EventType {
	case "TRANSACTION.SUCCESS":
		return c.parsePaymentNotify(notifyReq)
	case "REFUND.SUCCESS":
		return c.parseRefundNotify(notifyReq)
	default:
		return nil, fmt.Errorf("unsupported event type: %s", notifyReq.EventType)
	}
}

// parsePaymentNotify 解析支付成功回调
func (c *Client) parsePaymentNotify(notifyReq *wechatv3.V3NotifyReq) (*NotifyData, error) {
	// 解密支付回调数据
	result, err := wechatv3.V3DecryptPayNotifyCipherText(notifyReq.Resource.Ciphertext, notifyReq.Resource.AssociatedData, notifyReq.Resource.Nonce, c.config.Wechat.APIv3Key)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt payment notification: %w", err)
	}

	// 检查交易状态
	if result.TradeState != "SUCCESS" {
		return nil, fmt.Errorf("wechat payment not successful: trade_state=%s", result.TradeState)
	}

	// 获取关键字段
	if result.OutTradeNo == "" {
		return nil, errors.New("missing out_trade_no in payment notification")
	}

	// 解析金额（微信V3金额单位是分）
	amount := decimal.NewFromInt(int64(result.Amount.Total)).Div(decimal.NewFromInt(100))

	// 构建支付通知数据
	return &NotifyData{
		Type:          NotifyTypePaid,
		OrderNo:       result.OutTradeNo,
		TransactionID: result.TransactionId,
		Amount:        amount,
		Status:        "SUCCESS",
		PaidAt:        result.SuccessTime,
	}, nil
}

// parseRefundNotify 解析退款成功回调
func (c *Client) parseRefundNotify(notifyReq *wechatv3.V3NotifyReq) (*NotifyData, error) {
	// 解密退款回调数据
	result, err := wechatv3.V3DecryptRefundNotifyCipherText(notifyReq.Resource.Ciphertext, notifyReq.Resource.AssociatedData, notifyReq.Resource.Nonce, c.config.Wechat.APIv3Key)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt refund notification: %w", err)
	}

	// 检查退款状态
	if result.RefundStatus != "SUCCESS" {
		return nil, fmt.Errorf("wechat refund not successful: refund_status=%s", result.RefundStatus)
	}

	// 获取关键字段
	if result.OutTradeNo == "" {
		return nil, errors.New("missing out_trade_no in refund notification")
	}
	if result.OutRefundNo == "" {
		return nil, errors.New("missing out_refund_no in refund notification")
	}

	// 解析退款金额（微信V3金额单位是分）
	refundAmount := decimal.NewFromInt(int64(result.Amount.Refund)).Div(decimal.NewFromInt(100))

	// 构建退款通知数据
	return &NotifyData{
		Type:          NotifyTypeRefunded,
		OrderNo:       result.OutTradeNo,
		RefundNo:      result.OutRefundNo,
		TransactionID: result.TransactionId,
		Amount:        refundAmount,
		Status:        "SUCCESS",
		RefundedAt:    result.SuccessTime,
	}, nil
}
