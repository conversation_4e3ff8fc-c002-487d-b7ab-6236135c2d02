package database

import (
	"fmt"
	"time"

	"pay-core/internal/config"
	"pay-core/internal/model"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// NewMySQL 创建MySQL数据库连接
func NewMySQL(cfg config.DatabaseConfig) (*gorm.DB, error) {
	// 构建DSN
	dsn := cfg.GetDSN()

	// 配置GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 获取底层sql.DB
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get sql.DB: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(time.Duration(cfg.ConnMaxLifetime) * time.Second)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// 自动迁移 - 暂时禁用以避免迁移错误
	// if err := autoMigrate(db); err != nil {
	// 	return nil, fmt.Errorf("failed to auto migrate: %w", err)
	// }

	return db, nil
}

// autoMigrate 自动迁移数据库表
func autoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&model.MerchantApp{},
		&model.PaymentOrder{},
		&model.PaymentRecord{},
		&model.RefundOrder{},
		&model.RefundRecord{},
		&model.ReconciliationBatch{},
		&model.ReconciliationDetail{},
		&model.SettlementRecord{},
		&model.PaymentChannel{},
		&model.NotifyLog{},
	)
}

// InitTestDB 初始化测试数据库（使用MySQL测试数据库）
func InitTestDB() (*gorm.DB, error) {
	// 使用测试数据库配置
	cfg := config.DatabaseConfig{
		Host:            "localhost",
		Port:            3306,
		Username:        "root",
		Password:        "123456",
		Database:        "pay_core_test",
		Charset:         "utf8mb4",
		MaxIdleConns:    5,
		MaxOpenConns:    10,
		ConnMaxLifetime: 300,
	}

	// 创建数据库连接
	db, err := NewMySQL(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to test database: %w", err)
	}

	return db, nil
}
