package queue

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"pay-core/pkg/logger"

	"github.com/redis/go-redis/v9"
)

// DelayQueue Redis延迟队列
type DelayQueue struct {
	redis  *redis.Client
	prefix string
}

// DelayTask 延迟任务
type DelayTask struct {
	ID       string                 `json:"id"`
	Type     string                 `json:"type"`
	Data     map[string]interface{} `json:"data"`
	DelayTo  int64                  `json:"delay_to"`
	Retries  int                    `json:"retries"`
	MaxRetry int                    `json:"max_retry"`
}

// TaskType 任务类型常量
const (
	TaskTypeOrderExpire = "order_expire"  // 订单过期
	TaskTypeOrderPoll   = "order_poll"    // 订单轮询
)

// NewDelayQueue 创建延迟队列
func NewDelayQueue(redis *redis.Client, prefix string) *DelayQueue {
	if prefix == "" {
		prefix = "delay_queue"
	}
	return &DelayQueue{
		redis:  redis,
		prefix: prefix,
	}
}

// AddTask 添加延迟任务
func (dq *DelayQueue) AddTask(ctx context.Context, task *DelayTask) error {
	// 序列化任务数据
	taskData, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("failed to marshal task: %w", err)
	}

	// 添加到有序集合，score为执行时间戳
	queueKey := dq.getQueueKey(task.Type)
	err = dq.redis.ZAdd(ctx, queueKey, redis.Z{
		Score:  float64(task.DelayTo),
		Member: string(taskData),
	}).Err()

	if err != nil {
		return fmt.Errorf("failed to add task to delay queue: %w", err)
	}

	logger.WithFields(map[string]interface{}{
		"task_id":   task.ID,
		"task_type": task.Type,
		"delay_to":  time.Unix(task.DelayTo, 0).Format("2006-01-02 15:04:05"),
		"queue_key": queueKey,
	}).Debug("Added task to delay queue")

	return nil
}

// GetReadyTasks 获取准备执行的任务
func (dq *DelayQueue) GetReadyTasks(ctx context.Context, taskType string, limit int64) ([]*DelayTask, error) {
	queueKey := dq.getQueueKey(taskType)
	now := time.Now().Unix()

	// 获取到期的任务
	result, err := dq.redis.ZRangeByScoreWithScores(ctx, queueKey, &redis.ZRangeBy{
		Min:   "0",
		Max:   strconv.FormatInt(now, 10),
		Count: limit,
	}).Result()

	if err != nil {
		return nil, fmt.Errorf("failed to get ready tasks: %w", err)
	}

	if len(result) == 0 {
		return nil, nil
	}

	var tasks []*DelayTask
	var membersToRemove []interface{}

	for _, z := range result {
		taskData := z.Member.(string)
		var task DelayTask
		if err := json.Unmarshal([]byte(taskData), &task); err != nil {
			logger.WithError(err).WithField("task_data", taskData).Error("Failed to unmarshal task")
			membersToRemove = append(membersToRemove, taskData)
			continue
		}
		tasks = append(tasks, &task)
		membersToRemove = append(membersToRemove, taskData)
	}

	// 从队列中移除已获取的任务
	if len(membersToRemove) > 0 {
		err = dq.redis.ZRem(ctx, queueKey, membersToRemove...).Err()
		if err != nil {
			logger.WithError(err).Error("Failed to remove processed tasks from queue")
		}
	}

	logger.WithFields(map[string]interface{}{
		"task_type":  taskType,
		"task_count": len(tasks),
		"queue_key":  queueKey,
	}).Debug("Retrieved ready tasks from delay queue")

	return tasks, nil
}

// RemoveTask 移除任务
func (dq *DelayQueue) RemoveTask(ctx context.Context, taskType, taskID string) error {
	queueKey := dq.getQueueKey(taskType)

	// 获取所有任务，找到匹配的任务并移除
	allTasks, err := dq.redis.ZRange(ctx, queueKey, 0, -1).Result()
	if err != nil {
		return fmt.Errorf("failed to get all tasks: %w", err)
	}

	for _, taskData := range allTasks {
		var task DelayTask
		if err := json.Unmarshal([]byte(taskData), &task); err != nil {
			continue
		}
		if task.ID == taskID {
			err = dq.redis.ZRem(ctx, queueKey, taskData).Err()
			if err != nil {
				return fmt.Errorf("failed to remove task: %w", err)
			}
			logger.WithFields(map[string]interface{}{
				"task_id":   taskID,
				"task_type": taskType,
			}).Debug("Removed task from delay queue")
			return nil
		}
	}

	return nil // 任务不存在，视为成功
}

// RetryTask 重试任务
func (dq *DelayQueue) RetryTask(ctx context.Context, task *DelayTask, retryDelay time.Duration) error {
	if task.Retries >= task.MaxRetry {
		logger.WithFields(map[string]interface{}{
			"task_id":   task.ID,
			"task_type": task.Type,
			"retries":   task.Retries,
			"max_retry": task.MaxRetry,
		}).Warn("Task exceeded max retry count, discarding")
		return nil
	}

	// 增加重试次数
	task.Retries++
	task.DelayTo = time.Now().Add(retryDelay).Unix()

	return dq.AddTask(ctx, task)
}

// GetQueueStats 获取队列统计信息
func (dq *DelayQueue) GetQueueStats(ctx context.Context, taskType string) (map[string]interface{}, error) {
	queueKey := dq.getQueueKey(taskType)
	
	// 总任务数
	total, err := dq.redis.ZCard(ctx, queueKey).Result()
	if err != nil {
		return nil, err
	}

	// 准备执行的任务数
	now := time.Now().Unix()
	ready, err := dq.redis.ZCount(ctx, queueKey, "0", strconv.FormatInt(now, 10)).Result()
	if err != nil {
		return nil, err
	}

	// 等待中的任务数
	waiting := total - ready

	return map[string]interface{}{
		"queue_key":    queueKey,
		"total_tasks":  total,
		"ready_tasks":  ready,
		"waiting_tasks": waiting,
	}, nil
}

// ClearQueue 清空队列
func (dq *DelayQueue) ClearQueue(ctx context.Context, taskType string) error {
	queueKey := dq.getQueueKey(taskType)
	return dq.redis.Del(ctx, queueKey).Err()
}

// getQueueKey 获取队列键名
func (dq *DelayQueue) getQueueKey(taskType string) string {
	return fmt.Sprintf("%s:%s", dq.prefix, taskType)
}

// CreateOrderExpireTask 创建订单过期任务
func CreateOrderExpireTask(orderNo string, expireTime time.Time) *DelayTask {
	return &DelayTask{
		ID:       fmt.Sprintf("expire_%s", orderNo),
		Type:     TaskTypeOrderExpire,
		Data: map[string]interface{}{
			"order_no": orderNo,
		},
		DelayTo:  expireTime.Unix(),
		Retries:  0,
		MaxRetry: 3,
	}
}

// CreateOrderPollTask 创建订单轮询任务
func CreateOrderPollTask(orderNo string, pollTime time.Time, pollCount int) *DelayTask {
	return &DelayTask{
		ID:       fmt.Sprintf("poll_%s_%d", orderNo, pollCount),
		Type:     TaskTypeOrderPoll,
		Data: map[string]interface{}{
			"order_no":   orderNo,
			"poll_count": pollCount,
		},
		DelayTo:  pollTime.Unix(),
		Retries:  0,
		MaxRetry: 2,
	}
}
