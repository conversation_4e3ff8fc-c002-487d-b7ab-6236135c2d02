package robot

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"pay-core/pkg/logger"

	"github.com/shopspring/decimal"
)

// Client 机器人账户服务客户端
type Client struct {
	baseURL   string
	sharedKey string
	timeout   time.Duration
	client    *http.Client
}

// Config 客户端配置
type Config struct {
	BaseURL   string `yaml:"base_url" mapstructure:"base_url"`
	SharedKey string `yaml:"shared_key" mapstructure:"shared_key"`
	Timeout   int    `yaml:"timeout" mapstructure:"timeout"` // 超时时间（秒）
}

// RechargeRequest 充值请求
type RechargeRequest struct {
	Amount      decimal.Decimal `json:"amount"`
	OrderID     string          `json:"order_id"`
	Description string          `json:"description"`
}

// RechargeResponse 充值响应
type RechargeResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Success bool   `json:"success"`
		OrderID string `json:"order_id"`
	} `json:"data"`
}

// NewClient 创建新的机器人账户服务客户端
func NewClient(config Config) *Client {
	timeout := time.Duration(config.Timeout) * time.Second
	if timeout == 0 {
		timeout = 30 * time.Second // 默认30秒超时
	}

	return &Client{
		baseURL:   config.BaseURL,
		sharedKey: config.SharedKey,
		timeout:   timeout,
		client: &http.Client{
			Timeout: timeout,
		},
	}
}

// Recharge 调用充值接口
func (c *Client) Recharge(ctx context.Context, deviceSN string, req *RechargeRequest) (*RechargeResponse, error) {
	// 生成API Key
	apiKey, err := c.generateAPIKey(deviceSN)
	if err != nil {
		return nil, fmt.Errorf("failed to generate API key: %w", err)
	}

	// 构建请求
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	url := c.baseURL + "/api/v1/robot/recharge"
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+apiKey)

	logger.WithFields(map[string]interface{}{
		"url":       url,
		"device_sn": deviceSN,
		"order_id":  req.OrderID,
		"amount":    req.Amount,
		"api_key":   apiKey,
	}).Info("Sending recharge request to robot service")

	// 发送请求
	resp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	logger.WithFields(map[string]interface{}{
		"status_code": resp.StatusCode,
		"response":    string(respBody),
		"order_id":    req.OrderID,
	}).Info("Received response from robot service")

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("robot service returned status %d: %s", resp.StatusCode, string(respBody))
	}

	// 解析响应
	var rechargeResp RechargeResponse
	if err := json.Unmarshal(respBody, &rechargeResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// 检查业务状态码
	if rechargeResp.Code != 0 {
		return &rechargeResp, fmt.Errorf("robot service returned error: code=%d, message=%s", rechargeResp.Code, rechargeResp.Message)
	}

	return &rechargeResp, nil
}

// generateAPIKey 生成API Key（参考APIKeyGenerator.java实现）
func (c *Client) generateAPIKey(deviceSN string) (string, error) {
	if deviceSN == "" {
		return "", fmt.Errorf("device SN cannot be empty")
	}
	if c.sharedKey == "" {
		return "", fmt.Errorf("shared key cannot be empty")
	}

	// 1. 使用设备SN作为payload
	payload := deviceSN

	// 2. 创建HMAC-SHA256实例并计算签名
	h := hmac.New(sha256.New, []byte(c.sharedKey))
	h.Write([]byte(payload))
	signatureBytes := h.Sum(nil)
	signature := hex.EncodeToString(signatureBytes)

	// 3. 组合最终数据并Base64编码（使用标准URL编码，与Java保持一致）
	finalData := payload + ":" + signature
	apiKey := base64.URLEncoding.EncodeToString([]byte(finalData))

	logger.WithFields(map[string]interface{}{
		"device_sn":  deviceSN,
		"payload":    payload,
		"signature":  signature,
		"final_data": finalData,
		"api_key":    apiKey,
	}).Debug("Generated API key for robot service")

	return apiKey, nil
}

// GetRequestInfo 获取请求信息（用于日志记录）
func (c *Client) GetRequestInfo(deviceSN string, req *RechargeRequest) (string, string, error) {
	// 生成API Key
	apiKey, err := c.generateAPIKey(deviceSN)
	if err != nil {
		return "", "", err
	}

	// 构建请求体
	jsonData, err := json.Marshal(req)
	if err != nil {
		return "", "", err
	}

	// 构建完整的请求信息
	url := c.baseURL + "/api/v1/robot/recharge"
	requestInfo := fmt.Sprintf("POST %s\nContent-Type: application/json\nAuthorization: Bearer %s\n\n%s",
		url, apiKey, string(jsonData))

	return requestInfo, string(jsonData), nil
}

// GetBaseURL 获取基础URL
func (c *Client) GetBaseURL() string {
	return c.baseURL
}
